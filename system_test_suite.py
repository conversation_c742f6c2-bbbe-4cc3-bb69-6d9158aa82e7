#!/usr/bin/env python3
"""
System Test Suite for Memory Optimization Changes
End-to-end system-level validation of memory optimization in realistic scenarios
"""

import os
import sys
import time
import json
import psutil
import gc
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Any
import logging
import tempfile
import shutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemTestSuite:
    """System-level test suite for comprehensive validation."""
    
    def __init__(self, airflow_root: str):
        self.airflow_root = Path(airflow_root)
        self.start_time = time.time()
        self.test_results = {}
        self.memory_samples = []
        self.monitoring = False
        
    def setup_system_environment(self) -> bool:
        """Setup comprehensive system test environment."""
        logger.info("🔧 Setting up system test environment...")
        
        try:
            os.chdir(self.airflow_root)
            
            # Create temporary directories for system testing
            self.temp_dir = Path(tempfile.mkdtemp(prefix='airflow_system_test_'))
            self.test_db_path = self.temp_dir / 'system_test.db'
            
            # Set comprehensive environment variables
            os.environ.update({
                'AIRFLOW_HOME': str(self.temp_dir),
                'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
                'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': f'sqlite:///{self.test_db_path}',
                'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
                'AIRFLOW__CORE__LOAD_DEFAULT_CONNECTIONS': 'False',
                'AIRFLOW__LOGGING__LOGGING_LEVEL': 'WARNING',
                'AIRFLOW__CORE__DAGS_FOLDER': str(self.temp_dir / 'dags'),
                'AIRFLOW__CORE__PLUGINS_FOLDER': str(self.temp_dir / 'plugins'),
                'PYTHONPATH': str(self.airflow_root),
            })
            
            # Create necessary directories
            (self.temp_dir / 'dags').mkdir(exist_ok=True)
            (self.temp_dir / 'plugins').mkdir(exist_ok=True)
            (self.temp_dir / 'logs').mkdir(exist_ok=True)
            
            logger.info(f"✅ System environment setup complete: {self.temp_dir}")
            return True
            
        except Exception as e:
            logger.error(f"❌ System environment setup failed: {e}")
            return False
    
    def test_end_to_end_functionality(self) -> Dict[str, Any]:
        """Test end-to-end functionality with realistic scenarios."""
        logger.info("🔄 Testing End-to-End Functionality...")
        
        results = {}
        
        try:
            # Import our factory functions
            sys.path.insert(0, str(self.airflow_root))
            
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test 1: Realistic DAG simulation
            logger.info("Simulating realistic DAG execution...")
            
            process = psutil.Process()
            start_memory = process.memory_info().rss / 1024 / 1024
            
            # Simulate a DAG with multiple Spark tasks
            dag_tasks = []
            for dag_run in range(5):  # 5 DAG runs
                for task_num in range(10):  # 10 tasks per DAG
                    task_id = f'dag_run_{dag_run}_spark_task_{task_num}'
                    
                    # Create Spark application objects as would happen in real execution
                    k8s_dict = _get_expected_k8s_dict()
                    app_dict = _get_expected_application_dict_with_labels(task_id)
                    
                    # Simulate task processing
                    k8s_dict['metadata']['name'] = f'spark-app-{task_id}'
                    k8s_dict['metadata']['namespace'] = 'airflow'
                    
                    # Verify task_id propagation
                    assert app_dict['spec']['driver']['labels']['task_id'] == task_id
                    assert app_dict['spec']['executor']['labels']['task_id'] == task_id
                    
                    dag_tasks.append({
                        'task_id': task_id,
                        'k8s_dict': k8s_dict,
                        'app_dict': app_dict,
                        'dag_run': dag_run,
                        'task_num': task_num
                    })
            
            mid_memory = process.memory_info().rss / 1024 / 1024
            
            # Simulate task completion and cleanup
            completed_tasks = 0
            for task in dag_tasks:
                # Simulate task completion
                task['status'] = 'completed'
                completed_tasks += 1
                
                # Periodic cleanup simulation
                if completed_tasks % 10 == 0:
                    gc.collect()
            
            # Final cleanup
            dag_tasks.clear()
            gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            results['dag_simulation'] = {
                'start_memory_mb': start_memory,
                'peak_memory_mb': mid_memory,
                'end_memory_mb': end_memory,
                'memory_increase_mb': mid_memory - start_memory,
                'memory_recovered_mb': mid_memory - end_memory,
                'recovery_percentage': ((mid_memory - end_memory) / (mid_memory - start_memory) * 100) if (mid_memory - start_memory) > 0 else 0,
                'dag_runs_simulated': 5,
                'tasks_per_dag': 10,
                'total_tasks': 50,
                'tasks_completed': completed_tasks,
                'status': 'PASSED'
            }
            
            logger.info(f"✅ DAG simulation: {completed_tasks} tasks, {results['dag_simulation']['recovery_percentage']:.1f}% memory recovery")
            
            # Test 2: Concurrent execution simulation
            logger.info("Simulating concurrent task execution...")
            
            start_memory = process.memory_info().rss / 1024 / 1024
            
            # Simulate concurrent tasks from multiple DAGs
            concurrent_tasks = []
            for worker in range(4):  # 4 workers
                for task in range(25):  # 25 tasks per worker
                    task_id = f'worker_{worker}_task_{task}'
                    
                    k8s_dict = _get_expected_k8s_dict()
                    app_dict = _get_expected_application_dict_with_labels(task_id)
                    
                    # Simulate worker-specific modifications
                    k8s_dict['metadata']['labels'] = {'worker_id': str(worker)}
                    app_dict['spec']['driver']['labels']['worker_id'] = str(worker)
                    app_dict['spec']['executor']['labels']['worker_id'] = str(worker)
                    
                    concurrent_tasks.append({
                        'worker_id': worker,
                        'task_id': task_id,
                        'objects': (k8s_dict, app_dict)
                    })
            
            mid_memory = process.memory_info().rss / 1024 / 1024
            
            # Simulate worker completion
            workers_completed = 0
            for worker_id in range(4):
                worker_tasks = [t for t in concurrent_tasks if t['worker_id'] == worker_id]
                # Worker completes all its tasks
                for task in worker_tasks:
                    task['status'] = 'completed'
                workers_completed += 1
                
                # Worker cleanup
                if workers_completed % 2 == 0:
                    gc.collect()
            
            concurrent_tasks.clear()
            gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            results['concurrent_simulation'] = {
                'start_memory_mb': start_memory,
                'peak_memory_mb': mid_memory,
                'end_memory_mb': end_memory,
                'memory_increase_mb': mid_memory - start_memory,
                'memory_recovered_mb': mid_memory - end_memory,
                'recovery_percentage': ((mid_memory - end_memory) / (mid_memory - start_memory) * 100) if (mid_memory - start_memory) > 0 else 0,
                'workers_simulated': 4,
                'tasks_per_worker': 25,
                'total_concurrent_tasks': 100,
                'workers_completed': workers_completed,
                'status': 'PASSED'
            }
            
            logger.info(f"✅ Concurrent simulation: {workers_completed} workers, {results['concurrent_simulation']['recovery_percentage']:.1f}% memory recovery")
            
        except Exception as e:
            logger.error(f"❌ End-to-end functionality test failed: {e}")
            results['end_to_end_error'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def test_memory_stress_scenarios(self) -> Dict[str, Any]:
        """Test memory behavior under stress scenarios."""
        logger.info("💪 Testing Memory Stress Scenarios...")
        
        results = {}
        
        try:
            # Import factory functions
            sys.path.insert(0, str(self.airflow_root))
            
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test 1: High-volume object creation
            logger.info("Testing high-volume object creation...")
            
            process = psutil.Process()
            start_memory = process.memory_info().rss / 1024 / 1024
            
            # Create a large number of objects rapidly
            stress_objects = []
            for batch in range(20):  # 20 batches
                batch_objects = []
                for i in range(100):  # 100 objects per batch
                    task_id = f'stress_batch_{batch}_task_{i}'
                    
                    k8s_dict = _get_expected_k8s_dict()
                    app_dict = _get_expected_application_dict_with_labels(task_id)
                    
                    batch_objects.append((k8s_dict, app_dict))
                
                stress_objects.extend(batch_objects)
                
                # Monitor memory every 5 batches
                if batch % 5 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    logger.info(f"   Batch {batch}: {len(stress_objects)} objects, Memory: {current_memory:.1f}MB")
            
            peak_memory = process.memory_info().rss / 1024 / 1024
            
            # Clear in batches to test cleanup behavior
            for batch in range(20):
                batch_start = batch * 100
                batch_end = (batch + 1) * 100
                del stress_objects[batch_start:batch_end]
                
                if batch % 5 == 0:
                    gc.collect()
                    current_memory = process.memory_info().rss / 1024 / 1024
                    logger.info(f"   Cleanup batch {batch}: Memory: {current_memory:.1f}MB")
            
            stress_objects.clear()
            gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            results['high_volume_stress'] = {
                'start_memory_mb': start_memory,
                'peak_memory_mb': peak_memory,
                'end_memory_mb': end_memory,
                'memory_increase_mb': peak_memory - start_memory,
                'memory_recovered_mb': peak_memory - end_memory,
                'recovery_percentage': ((peak_memory - end_memory) / (peak_memory - start_memory) * 100) if (peak_memory - start_memory) > 0 else 0,
                'objects_created': 2000,
                'batches_processed': 20,
                'memory_per_object_kb': ((peak_memory - start_memory) * 1024) / 2000,
                'status': 'PASSED' if ((peak_memory - end_memory) / (peak_memory - start_memory) * 100) > 50 else 'FAILED'
            }
            
            logger.info(f"✅ High-volume stress: 2000 objects, {results['high_volume_stress']['recovery_percentage']:.1f}% recovery")
            
            # Test 2: Rapid creation/destruction cycles
            logger.info("Testing rapid creation/destruction cycles...")
            
            start_memory = process.memory_info().rss / 1024 / 1024
            memory_samples = []
            
            for cycle in range(50):  # 50 cycles
                cycle_objects = []
                
                # Rapid creation
                for i in range(50):
                    task_id = f'cycle_{cycle}_task_{i}'
                    k8s_dict = _get_expected_k8s_dict()
                    app_dict = _get_expected_application_dict_with_labels(task_id)
                    cycle_objects.append((k8s_dict, app_dict))
                
                # Immediate destruction
                cycle_objects.clear()
                
                # Sample memory every 10 cycles
                if cycle % 10 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_samples.append(current_memory)
                    gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            # Analyze memory stability
            memory_variance = max(memory_samples) - min(memory_samples) if memory_samples else 0
            
            results['rapid_cycles_stress'] = {
                'start_memory_mb': start_memory,
                'end_memory_mb': end_memory,
                'memory_delta_mb': end_memory - start_memory,
                'memory_variance_mb': memory_variance,
                'cycles_completed': 50,
                'objects_per_cycle': 50,
                'total_objects_processed': 2500,
                'memory_samples': memory_samples,
                'memory_stable': memory_variance < 10,  # Less than 10MB variance
                'status': 'PASSED' if memory_variance < 10 and abs(end_memory - start_memory) < 5 else 'FAILED'
            }
            
            logger.info(f"✅ Rapid cycles: 50 cycles, {memory_variance:.1f}MB variance")
            
        except Exception as e:
            logger.error(f"❌ Memory stress test failed: {e}")
            results['memory_stress_error'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def test_ci_environment_compatibility(self) -> Dict[str, Any]:
        """Test CI environment compatibility and resource constraints."""
        logger.info("🏗️ Testing CI Environment Compatibility...")
        
        results = {}
        
        try:
            # Test 1: Resource-constrained execution
            logger.info("Testing resource-constrained execution...")
            
            # Simulate CI environment constraints
            process = psutil.Process()
            start_memory = process.memory_info().rss / 1024 / 1024
            
            # Import factory functions
            sys.path.insert(0, str(self.airflow_root))
            
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Simulate CI test execution pattern
            ci_objects = []
            max_memory_used = start_memory
            
            for test_run in range(10):  # 10 test runs (like CI)
                test_objects = []
                
                # Each test run creates objects
                for test_case in range(20):
                    task_id = f'ci_run_{test_run}_test_{test_case}'
                    
                    k8s_dict = _get_expected_k8s_dict()
                    app_dict = _get_expected_application_dict_with_labels(task_id)
                    
                    test_objects.append((k8s_dict, app_dict))
                
                ci_objects.extend(test_objects)
                
                # Monitor peak memory usage
                current_memory = process.memory_info().rss / 1024 / 1024
                max_memory_used = max(max_memory_used, current_memory)
                
                # Simulate test completion and cleanup
                test_objects.clear()
                
                # Periodic garbage collection (like CI cleanup)
                if test_run % 3 == 0:
                    gc.collect()
            
            # Final cleanup
            ci_objects.clear()
            gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            results['ci_resource_constraints'] = {
                'start_memory_mb': start_memory,
                'peak_memory_mb': max_memory_used,
                'end_memory_mb': end_memory,
                'memory_increase_mb': max_memory_used - start_memory,
                'memory_recovered_mb': max_memory_used - end_memory,
                'test_runs_simulated': 10,
                'test_cases_per_run': 20,
                'total_test_cases': 200,
                'peak_memory_reasonable': max_memory_used < start_memory + 50,  # Less than 50MB increase
                'memory_cleanup_effective': end_memory < start_memory + 5,  # Less than 5MB final increase
                'status': 'PASSED' if (max_memory_used < start_memory + 50 and end_memory < start_memory + 5) else 'FAILED'
            }
            
            logger.info(f"✅ CI compatibility: Peak {max_memory_used:.1f}MB, Final {end_memory:.1f}MB")
            
            # Test 2: Pytest execution simulation
            logger.info("Testing pytest execution simulation...")
            
            # Run actual pytest on our test file to simulate CI
            cmd = [
                sys.executable, '-m', 'pytest',
                'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py',
                '-v',
                '--tb=short',
                '--disable-warnings',
                '--maxfail=1',
                '--junit-xml=files/test_result-system-ci-simulation.xml'
            ]
            
            env = os.environ.copy()
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root, env=env, timeout=180)
            duration = time.time() - start_time
            
            test_count = result.stdout.count('PASSED')
            failed_count = result.stdout.count('FAILED')
            
            results['pytest_ci_simulation'] = {
                'returncode': result.returncode,
                'duration_seconds': duration,
                'tests_passed': test_count,
                'tests_failed': failed_count,
                'total_tests': test_count + failed_count,
                'success_rate': (test_count / (test_count + failed_count) * 100) if (test_count + failed_count) > 0 else 0,
                'execution_time_reasonable': duration < 120,  # Less than 2 minutes
                'status': 'PASSED' if result.returncode == 0 and duration < 120 else 'FAILED'
            }
            
            logger.info(f"✅ Pytest CI simulation: {test_count} passed in {duration:.1f}s")
            
        except Exception as e:
            logger.error(f"❌ CI compatibility test failed: {e}")
            results['ci_compatibility_error'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def cleanup_system_environment(self):
        """Clean up system test environment."""
        try:
            if hasattr(self, 'temp_dir') and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info(f"✅ Cleaned up system test environment: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"⚠️ Cleanup warning: {e}")
    
    def run_system_tests(self) -> Dict[str, Any]:
        """Run all system tests."""
        logger.info("🚀 Starting System Test Suite...")
        
        if not self.setup_system_environment():
            return {'error': 'System environment setup failed'}
        
        try:
            # Run all system test categories
            self.test_results['end_to_end'] = self.test_end_to_end_functionality()
            self.test_results['memory_stress'] = self.test_memory_stress_scenarios()
            self.test_results['ci_compatibility'] = self.test_ci_environment_compatibility()
            
            return self.test_results
            
        finally:
            self.cleanup_system_environment()
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive system test report."""
        logger.info("📋 Generating System Test Report...")
        
        total_duration = time.time() - self.start_time
        
        # Calculate overall statistics
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            for test_name, result in tests.items():
                if isinstance(result, dict) and 'status' in result:
                    total_tests += 1
                    if result['status'] == 'PASSED':
                        passed_tests += 1
        
        report = {
            'test_suite': 'System Test Suite - Memory Optimization',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_duration_seconds': total_duration,
            'environment': {
                'python_version': sys.version,
                'platform': sys.platform,
                'airflow_root': str(self.airflow_root),
            },
            'test_results': self.test_results,
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'overall_status': 'PASSED' if passed_tests == total_tests else 'FAILED'
            }
        }
        
        return report

def main():
    """Main execution function."""
    airflow_root = '/Users/<USER>/oss/pr/airflow'
    
    logger.info("🚀 Starting System Test Suite...")
    
    test_suite = SystemTestSuite(airflow_root)
    
    try:
        # Run system tests
        test_suite.run_system_tests()
        
        # Generate and save report
        report = test_suite.generate_report()
        
        # Save report to file
        report_file = Path(airflow_root) / 'system_test_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"📄 Report saved to: {report_file}")
        
        # Print summary
        summary = report['summary']
        logger.info(f"🎯 Test Summary: {summary['passed_tests']}/{summary['total_tests']} tests passed")
        logger.info(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        logger.info(f"🏁 Overall Status: {summary['overall_status']}")
        
        return 0 if summary['overall_status'] == 'PASSED' else 1
        
    except Exception as e:
        logger.error(f"❌ System test suite failed: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
