{"test_suite": "System Test Suite - Memory Optimization", "timestamp": "2025-05-31 02:04:32", "total_duration_seconds": 7.137747049331665, "environment": {"python_version": "3.12.2 (v3.12.2:6abddd9f6a, Feb  6 2024, 17:02:06) [Clang 13.0.0 (clang-1300.0.29.30)]", "platform": "darwin", "airflow_root": "/Users/<USER>/oss/pr/airflow"}, "test_results": {"end_to_end": {"dag_simulation": {"start_memory_mb": 193.265625, "peak_memory_mb": 193.59375, "end_memory_mb": 193.59375, "memory_increase_mb": 0.328125, "memory_recovered_mb": 0.0, "recovery_percentage": 0.0, "dag_runs_simulated": 5, "tasks_per_dag": 10, "total_tasks": 50, "tasks_completed": 50, "status": "PASSED"}, "concurrent_simulation": {"start_memory_mb": 193.59375, "peak_memory_mb": 193.90625, "end_memory_mb": 193.90625, "memory_increase_mb": 0.3125, "memory_recovered_mb": 0.0, "recovery_percentage": 0.0, "workers_simulated": 4, "tasks_per_worker": 25, "total_concurrent_tasks": 100, "workers_completed": 4, "status": "PASSED"}}, "memory_stress": {"high_volume_stress": {"start_memory_mb": 193.90625, "peak_memory_mb": 206.453125, "end_memory_mb": 198.453125, "memory_increase_mb": 12.546875, "memory_recovered_mb": 8.0, "recovery_percentage": 63.760896637608965, "objects_created": 2000, "batches_processed": 20, "memory_per_object_kb": 6.424, "status": "PASSED"}, "rapid_cycles_stress": {"start_memory_mb": 198.453125, "end_memory_mb": 198.5, "memory_delta_mb": 0.046875, "memory_variance_mb": 0.0, "cycles_completed": 50, "objects_per_cycle": 50, "total_objects_processed": 2500, "memory_samples": [198.5, 198.5, 198.5, 198.5, 198.5], "memory_stable": true, "status": "PASSED"}}, "ci_compatibility": {"ci_resource_constraints": {"start_memory_mb": 197.5, "peak_memory_mb": 197.5, "end_memory_mb": 197.5, "memory_increase_mb": 0.0, "memory_recovered_mb": 0.0, "test_runs_simulated": 10, "test_cases_per_run": 20, "total_test_cases": 200, "peak_memory_reasonable": true, "memory_cleanup_effective": true, "status": "PASSED"}, "pytest_ci_simulation": {"returncode": 1, "duration_seconds": 4.7307140827178955, "tests_passed": 19, "tests_failed": 2, "total_tests": 21, "success_rate": 90.47619047619048, "execution_time_reasonable": true, "status": "FAILED"}}}, "summary": {"total_tests": 6, "passed_tests": 5, "failed_tests": 1, "success_rate": 83.33333333333334, "overall_status": "FAILED"}}