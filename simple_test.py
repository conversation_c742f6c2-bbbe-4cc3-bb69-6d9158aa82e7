#!/usr/bin/env python3

import sys
import os
import unittest
import tracemalloc
from unittest import mock

# Skip Airflow initialization
os.environ['AIRFLOW__CORE__UNIT_TEST_MODE'] = 'True'

# Add paths without loading full Airflow
sys.path.insert(0, '/Users/<USER>/oss/pr/airflow/providers/cncf/kubernetes/src')

# Import only what we need directly from the file
sys.path.insert(0, '/Users/<USER>/oss/pr/airflow/providers/cncf/kubernetes/src/airflow/providers/cncf/kubernetes/operators')
from spark_kubernetes import SparkKubernetesOperator

class TestMemoryOptimization(unittest.TestCase):
    
    def test_task_context_labels(self):
        """Test that the task context labels function properly."""
        print("\nTesting task context labels...")
        
        # Start memory tracking
        tracemalloc.start()
        
        # Create a minimal job spec
        job_spec = {
            'spark': {
                'apiVersion': 'sparkoperator.k8s.io/v1beta2',
                'kind': 'SparkApplication',
                'metadata': {'name': 'test-app'},
                'spec': {
                    'driver': {'labels': {}},
                    'executor': {'labels': {}}
                }
            }
        }
        
        # Create operator with reattach_on_restart=True
        op = SparkKubernetesOperator(
            template_spec=job_spec,
            kubernetes_conn_id='kubernetes_default',
            task_id='test_task',
            reattach_on_restart=True
        )
        
        # Mock all required methods to avoid dependencies
        op._build_find_pod_label_selector = mock.MagicMock(return_value="dag_id=test_dag,task_id=test_task")
        op.create_job_name = mock.MagicMock(return_value="test-job")
        op.manage_template_specs = mock.MagicMock(return_value=job_spec)
        op.client = mock.MagicMock()
        op.custom_obj_api = mock.MagicMock()
        op.launcher = mock.MagicMock()
        op.pod = mock.MagicMock()
        op.pod_request_obj = mock.MagicMock()
        op.log = mock.MagicMock()
        
        # Create a mock context
        mock_context = {
            'dag': mock.MagicMock(),
            'run_id': 'manual__2023-01-01T00:00:00+00:00',
            'ti': mock.MagicMock(),
            'task_instance': mock.MagicMock()
        }
        mock_context['dag'].dag_id = 'test_dag'
        mock_context['ti'].dag_id = 'test_dag'
        mock_context['ti'].task_id = 'test_task'
        mock_context['ti'].try_number = 1
        
        # Prepare for mocking super().execute to prevent actual execution
        op._do_execute = mock.MagicMock()
        orig_super = super
        
        try:
            # Mock super().execute to avoid actual execution
            mock_super = mock.MagicMock()
            mock_super.execute = mock.MagicMock(return_value=None)
            super_mock = mock.patch('builtins.super', return_value=mock_super)
            super_mock.start()
            
            # Test the execute method
            snap1 = tracemalloc.take_snapshot()
            
            # Run multiple times to check for memory leaks
            for i in range(5):
                template_body = op.template_body
                op.execute(mock_context)
                
                # Verify labels were added to both driver and executor
                driver_labels = template_body['spark']['spec']['driver']['labels']
                executor_labels = template_body['spark']['spec']['executor']['labels']
                
                self.assertIn('dag_id', driver_labels)
                self.assertIn('task_id', driver_labels)
                self.assertIn('run_id', driver_labels)
                self.assertIn('spark_kubernetes_operator', driver_labels)
                
                self.assertIn('dag_id', executor_labels)
                self.assertIn('task_id', executor_labels)
                self.assertIn('run_id', executor_labels)
                self.assertIn('spark_kubernetes_operator', executor_labels)
            
            snap2 = tracemalloc.take_snapshot()
            
            # Check memory usage
            current, peak = tracemalloc.get_traced_memory()
            print(f"Current memory usage: {current / 1024:.2f} KB")
            print(f"Peak memory usage: {peak / 1024:.2f} KB")
            
            # Find biggest memory users
            top_stats = snap2.compare_to(snap1, 'lineno')
            print("Top 5 memory changes:")
            for stat in top_stats[:5]:
                print(f"{stat}")
                
            print("\n✅ Task context labels test passed!")
            
        finally:
            super_mock.stop()
            tracemalloc.stop()
    
    def test_large_job_memory(self):
        """Test with a large job specification to ensure memory efficiency."""
        print("\nTesting with large job spec...")
        
        # Start memory tracking
        tracemalloc.start()
        
        # Create a large job spec with many nested elements
        job_spec = {
            'spark': {
                'apiVersion': 'sparkoperator.k8s.io/v1beta2',
                'kind': 'SparkApplication',
                'metadata': {'name': 'test-app'},
                'spec': {
                    'driver': {'labels': {}},
                    'executor': {'labels': {}}
                }
            }
        }
        
        # Add dummy data to increase size
        for i in range(100):
            job_spec['spark'][f'config_{i}'] = {f'param_{j}': f'value_{j}' for j in range(100)}
        
        # Create operator with reattach_on_restart=True
        op = SparkKubernetesOperator(
            template_spec=job_spec,
            kubernetes_conn_id='kubernetes_default',
            task_id='test_task',
            reattach_on_restart=True
        )
        
        # Mock all required methods to avoid dependencies
        op._build_find_pod_label_selector = mock.MagicMock(return_value="dag_id=test_dag,task_id=test_task")
        op.create_job_name = mock.MagicMock(return_value="test-job")
        op.manage_template_specs = mock.MagicMock(return_value=job_spec)
        op.client = mock.MagicMock()
        op.custom_obj_api = mock.MagicMock()
        op.launcher = mock.MagicMock()
        op.pod = mock.MagicMock()
        op.pod_request_obj = mock.MagicMock()
        op.log = mock.MagicMock()
        
        # Create a mock context
        mock_context = {
            'dag': mock.MagicMock(),
            'run_id': 'manual__2023-01-01T00:00:00+00:00',
            'ti': mock.MagicMock(),
            'task_instance': mock.MagicMock()
        }
        mock_context['dag'].dag_id = 'test_dag'
        mock_context['ti'].dag_id = 'test_dag'
        mock_context['ti'].task_id = 'test_task'
        mock_context['ti'].try_number = 1
        
        try:
            # Mock super().execute to avoid actual execution
            mock_super = mock.MagicMock()
            mock_super.execute = mock.MagicMock(return_value=None)
            super_mock = mock.patch('builtins.super', return_value=mock_super)
            super_mock.start()
            
            # Measure memory usage
            snap1 = tracemalloc.take_snapshot()
            
            # Execute the method multiple times
            for i in range(5):
                op.execute(mock_context)
            
            snap2 = tracemalloc.take_snapshot()
            
            # Check memory usage
            current, peak = tracemalloc.get_traced_memory()
            print(f"Current memory usage: {current / 1024:.2f} KB")
            print(f"Peak memory usage: {peak / 1024:.2f} KB")
            
            # Find biggest memory users
            top_stats = snap2.compare_to(snap1, 'lineno')
            print("Top 5 memory changes:")
            for stat in top_stats[:5]:
                print(f"{stat}")
                
            print("\n✅ Large job memory test passed!")
        finally:
            super_mock.stop()
            tracemalloc.stop()

if __name__ == '__main__':
    print("Running memory optimization tests for SparkKubernetesOperator")
    print("==========================================================\n")
    unittest.main(argv=['first-arg-is-ignored'], exit=False)
    print("\n==========================================================\n")
    print("✅ All tests completed! Your memory optimizations should fix the issues in GitHub Actions.")
