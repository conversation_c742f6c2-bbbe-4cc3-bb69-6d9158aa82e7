{"test_suite": "Focused Memory Optimization Test Suite", "timestamp": "2025-05-31 01:30:49", "duration_seconds": 3.40824031829834, "test_results": {"direct_tests": {"factory_functions": {"start_memory_mb": 16.6875, "mid_memory_mb": 16.953125, "end_memory_mb": 16.953125, "k8s_dict_keys": 4, "app_dict_keys": 4, "task_id_correct": true, "passed": true}}, "memory_tests": {"memory_efficiency": {"start_memory_mb": 16.984375, "peak_memory_mb": 23.515625, "end_memory_mb": 19.515625, "memory_increase_mb": 6.53125, "memory_recovered_mb": 4.0, "recovery_percentage": 61.24401913875598, "objects_created": 1000, "memory_per_object_kb": 6.688, "passed": false}}, "performance_tests": {"performance": {"k8s_dict_iterations": 10000, "k8s_dict_total_time": 0.009577035903930664, "k8s_dict_time_per_call_ms": 0.0009577035903930663, "app_dict_iterations": 10000, "app_dict_total_time": 0.008619070053100586, "app_dict_time_per_call_ms": 0.0008619070053100586, "total_calls": 20000, "total_time": 0.01819610595703125, "calls_per_second": 1099136.2683438156, "passed": true}}, "pytest_tests": {"pytest_collection": {"returncode": 0, "duration": 3.381894111633301, "tests_found": 0, "file_collected": true, "passed": true}}}, "memory_metrics": {"rss_mb": 19.859375, "vms_mb": 401101.265625, "percent": 0.10774400499131945}, "summary": {"total_tests": 4, "passed_tests": 3, "failed_tests": 1, "success_rate": 75.0, "overall_status": "FAILED"}}