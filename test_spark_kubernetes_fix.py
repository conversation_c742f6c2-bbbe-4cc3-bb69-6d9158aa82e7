#!/usr/bin/env python3

"""
Test script to verify the fix for bug https://github.com/apache/airflow/issues/41211.

This script creates a SparkKubernetesOperator with reattach_on_restart=True
and verifies that the task context labels are added to the driver and executor pods.
"""

import os
import sys
import yaml
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime
from airflow.models import DAG
from airflow.providers.cncf.kubernetes.operators.spark_kubernetes import SparkKubernetesOperator

# Add the Airflow directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))


# Create a mock context
def create_mock_context():
    dag = DAG(dag_id="test_dag_id", schedule=None, start_date=datetime(2020, 2, 1))
    task_instance = MagicMock()
    task_instance.dag_id = "test_dag_id"
    task_instance.task_id = "test_task_id"
    task_instance.run_id = "test_run_id"
    task_instance.try_number = 1
    dag_run = MagicMock()
    dag_run.run_id = "test_run_id"

    return {
        "dag": dag,
        "run_id": "test_run_id",
        "task_instance": task_instance,
        "ti": task_instance,
        "dag_run": dag_run,
    }


def test_task_context_labels_functionality():
    """Test that task context labels are properly added to driver and executor specs."""
    
    # Create a simple spark application template
    job_spec = {
        "apiVersion": "sparkoperator.k8s.io/v1beta2",
        "kind": "SparkApplication",
        "metadata": {"name": "test-spark-app", "namespace": "default"},
        "spec": {
            "driver": {"labels": {}},
            "executor": {"labels": {}},
            "image": "spark:latest",
            "type": "Python",
            "mode": "cluster",
            "mainApplicationFile": "local:///test.py"
        }
    }
    
    # Create operator with reattach_on_restart=True
    operator = SparkKubernetesOperator(
        task_id="test_task",
        template_spec=job_spec,
        kubernetes_conn_id="kubernetes_default",
        reattach_on_restart=True,
    )
    
    # Create mock context
    mock_ti = Mock()
    mock_ti.dag_id = "test_dag"
    mock_ti.task_id = "test_task"  
    mock_ti.try_number = 1
    mock_ti.map_index = -1
    
    mock_dag = Mock()
    mock_dag.is_subdag = False
    
    context = {
        "ti": mock_ti,
        "run_id": "test_run_123",
        "dag": mock_dag,
        "task": operator
    }
    
    # Test the _get_ti_pod_labels method
    task_context_labels = operator._get_ti_pod_labels(context)
    print("Generated task context labels:", task_context_labels)
    
    # Verify expected labels are present
    expected_labels = {
        "dag_id": "test_dag",
        "task_id": "test_task", 
        "run_id": "test_run_123",
        "spark_kubernetes_operator": "True",
        "try_number": 1
    }
    
    for key, expected_value in expected_labels.items():
        assert key in task_context_labels, f"Missing label: {key}"
        assert str(task_context_labels[key]) == str(expected_value), f"Label {key} mismatch: {task_context_labels[key]} != {expected_value}"
    
    print("✓ Task context labels generation works correctly")
    
    # Test that execute method properly adds labels to template body
    with patch.multiple(operator, 
                       hook=Mock(), 
                       client=Mock(),
                       custom_obj_api=Mock(),
                       get_or_create_spark_crd=Mock(return_value=Mock()),
                       launcher=Mock()):
        
        # Mock the parent class execute method to avoid actual k8s operations
        with patch('airflow.providers.cncf.kubernetes.operators.pod.KubernetesPodOperator.execute', return_value=None):
            operator.execute(context)
            
            # Check that task context labels were added to the template body
            template_body = operator.template_body
            
            # Verify labels were added to driver
            driver_labels = template_body["spark"]["spec"]["driver"]["labels"]
            for key, expected_value in expected_labels.items():
                assert key in driver_labels, f"Driver missing label: {key}"
                assert str(driver_labels[key]) == str(expected_value), f"Driver label {key} mismatch"
            
            # Verify labels were added to executor  
            executor_labels = template_body["spark"]["spec"]["executor"]["labels"]
            for key, expected_value in expected_labels.items():
                assert key in executor_labels, f"Executor missing label: {key}"
                assert str(executor_labels[key]) == str(expected_value), f"Executor label {key} mismatch"
                
            print("✓ Task context labels properly added to driver and executor specs")
    
    print("✓ All tests passed!")


if __name__ == "__main__":
    test_task_context_labels_functionality()
