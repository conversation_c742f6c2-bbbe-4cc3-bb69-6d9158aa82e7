#!/usr/bin/env python3
"""
Integration Test Suite for Memory Optimization Changes
Tests integration with related Kubernetes provider components
"""

import os
import sys
import time
import json
import psutil
import gc
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntegrationTestSuite:
    """Integration test suite for memory optimization validation."""
    
    def __init__(self, airflow_root: str):
        self.airflow_root = Path(airflow_root)
        self.start_time = time.time()
        self.test_results = {}
        
    def setup_integration_environment(self) -> bool:
        """Setup environment for integration testing."""
        logger.info("🔧 Setting up integration test environment...")
        
        try:
            os.chdir(self.airflow_root)
            
            # Set comprehensive environment variables
            os.environ.update({
                'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
                'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': 'sqlite:////tmp/airflow_integration_test.db',
                'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
                'AIRFLOW__CORE__LOAD_DEFAULT_CONNECTIONS': 'False',
                'AIRFLOW__LOGGING__LOGGING_LEVEL': 'WARNING',
                'PYTHONPATH': str(self.airflow_root),
            })
            
            logger.info("✅ Integration environment setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Integration environment setup failed: {e}")
            return False
    
    def test_kubernetes_provider_integration(self) -> Dict[str, Any]:
        """Test integration with broader Kubernetes provider components."""
        logger.info("🔗 Testing Kubernetes Provider Integration...")
        
        results = {}
        
        # Test 1: Related Kubernetes Operator Tests
        logger.info("Running related Kubernetes operator tests...")
        
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/',
                '-v',
                '--tb=short',
                '--disable-warnings',
                '--maxfail=10',  # Stop after 10 failures to save time
                '--junit-xml=files/test_result-k8s-integration.xml'
            ]
            
            env = os.environ.copy()
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root, env=env, timeout=300)
            duration = time.time() - start_time
            
            # Parse results
            test_count = result.stdout.count('PASSED')
            failed_count = result.stdout.count('FAILED')
            error_count = result.stdout.count('ERROR')
            
            results['kubernetes_operators'] = {
                'returncode': result.returncode,
                'duration_seconds': duration,
                'tests_passed': test_count,
                'tests_failed': failed_count,
                'tests_error': error_count,
                'total_tests': test_count + failed_count + error_count,
                'success_rate': (test_count / (test_count + failed_count + error_count) * 100) if (test_count + failed_count + error_count) > 0 else 0,
                'status': 'PASSED' if result.returncode == 0 else 'FAILED'
            }
            
            logger.info(f"✅ Kubernetes operators: {test_count} passed, {failed_count} failed, {error_count} errors")
            
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ Kubernetes operator tests timed out")
            results['kubernetes_operators'] = {
                'error': 'Test execution timed out after 300 seconds',
                'status': 'TIMEOUT'
            }
        except Exception as e:
            logger.error(f"❌ Kubernetes operator tests failed: {e}")
            results['kubernetes_operators'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def test_memory_integration_patterns(self) -> Dict[str, Any]:
        """Test memory integration patterns with factory functions."""
        logger.info("📊 Testing Memory Integration Patterns...")
        
        results = {}
        
        try:
            # Import our factory functions and related components
            sys.path.insert(0, str(self.airflow_root))
            
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test 1: Cross-component memory behavior
            logger.info("Testing cross-component memory behavior...")
            
            process = psutil.Process()
            start_memory = process.memory_info().rss / 1024 / 1024
            
            # Simulate integration scenario with multiple components
            integration_objects = []
            
            for i in range(500):
                # Create objects as if multiple components are using them
                k8s_dict = _get_expected_k8s_dict()
                app_dict_1 = _get_expected_application_dict_with_labels(f'component_a_task_{i}')
                app_dict_2 = _get_expected_application_dict_with_labels(f'component_b_task_{i}')
                
                # Simulate different components modifying the objects
                k8s_dict['metadata']['labels'] = {'integration_test': 'true'}
                app_dict_1['spec']['driver']['labels']['component'] = 'a'
                app_dict_2['spec']['driver']['labels']['component'] = 'b'
                
                integration_objects.append((k8s_dict, app_dict_1, app_dict_2))
            
            mid_memory = process.memory_info().rss / 1024 / 1024
            
            # Clear objects and force garbage collection
            integration_objects.clear()
            gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            memory_increase = mid_memory - start_memory
            memory_recovered = mid_memory - end_memory
            recovery_percentage = (memory_recovered / memory_increase * 100) if memory_increase > 0 else 0
            
            results['cross_component_memory'] = {
                'start_memory_mb': start_memory,
                'peak_memory_mb': mid_memory,
                'end_memory_mb': end_memory,
                'memory_increase_mb': memory_increase,
                'memory_recovered_mb': memory_recovered,
                'recovery_percentage': recovery_percentage,
                'objects_created': 1500,  # 500 * 3 objects
                'components_simulated': 2,
                'status': 'PASSED' if recovery_percentage > 70 else 'FAILED'
            }
            
            logger.info(f"✅ Cross-component memory: {recovery_percentage:.1f}% recovery")
            
            # Test 2: Concurrent access patterns
            logger.info("Testing concurrent access patterns...")
            
            start_memory = process.memory_info().rss / 1024 / 1024
            
            # Simulate concurrent access from multiple threads/processes
            concurrent_objects = []
            
            for thread_id in range(10):
                for task_id in range(50):
                    k8s_dict = _get_expected_k8s_dict()
                    app_dict = _get_expected_application_dict_with_labels(f'thread_{thread_id}_task_{task_id}')
                    
                    # Verify task_id is correctly set
                    assert app_dict['spec']['driver']['labels']['task_id'] == f'thread_{thread_id}_task_{task_id}'
                    assert app_dict['spec']['executor']['labels']['task_id'] == f'thread_{thread_id}_task_{task_id}'
                    
                    concurrent_objects.append((k8s_dict, app_dict))
            
            mid_memory = process.memory_info().rss / 1024 / 1024
            
            # Clear and measure
            concurrent_objects.clear()
            gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            memory_increase = mid_memory - start_memory
            memory_recovered = mid_memory - end_memory
            recovery_percentage = (memory_recovered / memory_increase * 100) if memory_increase > 0 else 0
            
            results['concurrent_access'] = {
                'start_memory_mb': start_memory,
                'peak_memory_mb': mid_memory,
                'end_memory_mb': end_memory,
                'memory_increase_mb': memory_increase,
                'memory_recovered_mb': memory_recovered,
                'recovery_percentage': recovery_percentage,
                'threads_simulated': 10,
                'tasks_per_thread': 50,
                'total_objects': 1000,
                'status': 'PASSED' if recovery_percentage > 70 else 'FAILED'
            }
            
            logger.info(f"✅ Concurrent access: {recovery_percentage:.1f}% recovery")
            
        except Exception as e:
            logger.error(f"❌ Memory integration test failed: {e}")
            results['memory_integration_error'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def test_api_compatibility(self) -> Dict[str, Any]:
        """Test API compatibility with existing code."""
        logger.info("🔌 Testing API Compatibility...")
        
        results = {}
        
        try:
            # Import and test API compatibility
            sys.path.insert(0, str(self.airflow_root))
            
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test 1: Function signatures
            logger.info("Testing function signatures...")
            
            # Test that functions can be called without parameters
            k8s_dict = _get_expected_k8s_dict()
            assert isinstance(k8s_dict, dict)
            assert 'apiVersion' in k8s_dict
            assert 'kind' in k8s_dict
            
            # Test that function can be called with task_id parameter
            app_dict = _get_expected_application_dict_with_labels('test_task')
            assert isinstance(app_dict, dict)
            assert 'apiVersion' in app_dict
            assert 'kind' in app_dict
            assert app_dict['spec']['driver']['labels']['task_id'] == 'test_task'
            
            results['function_signatures'] = {
                'k8s_dict_callable': True,
                'app_dict_callable': True,
                'task_id_parameter_works': True,
                'return_types_correct': True,
                'status': 'PASSED'
            }
            
            logger.info("✅ Function signatures: All compatible")
            
            # Test 2: Data structure compatibility
            logger.info("Testing data structure compatibility...")
            
            # Verify the structure matches expected format
            expected_k8s_keys = {'apiVersion', 'kind', 'metadata', 'spec'}
            actual_k8s_keys = set(k8s_dict.keys())
            
            expected_app_keys = {'apiVersion', 'kind', 'metadata', 'spec'}
            actual_app_keys = set(app_dict.keys())
            
            results['data_structure'] = {
                'k8s_dict_keys_match': expected_k8s_keys == actual_k8s_keys,
                'app_dict_keys_match': expected_app_keys == actual_app_keys,
                'k8s_dict_structure_valid': all(key in k8s_dict for key in expected_k8s_keys),
                'app_dict_structure_valid': all(key in app_dict for key in expected_app_keys),
                'task_id_propagation': (
                    app_dict['spec']['driver']['labels']['task_id'] == 'test_task' and
                    app_dict['spec']['executor']['labels']['task_id'] == 'test_task'
                ),
                'status': 'PASSED'
            }
            
            logger.info("✅ Data structures: All compatible")
            
        except Exception as e:
            logger.error(f"❌ API compatibility test failed: {e}")
            results['api_compatibility_error'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """Run all integration tests."""
        logger.info("🚀 Starting Integration Test Suite...")
        
        if not self.setup_integration_environment():
            return {'error': 'Environment setup failed'}
        
        # Run all integration test categories
        self.test_results['kubernetes_provider'] = self.test_kubernetes_provider_integration()
        self.test_results['memory_integration'] = self.test_memory_integration_patterns()
        self.test_results['api_compatibility'] = self.test_api_compatibility()
        
        return self.test_results
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive integration test report."""
        logger.info("📋 Generating Integration Test Report...")
        
        total_duration = time.time() - self.start_time
        
        # Calculate overall statistics
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for category, tests in self.test_results.items():
            for test_name, result in tests.items():
                if isinstance(result, dict) and 'status' in result:
                    total_tests += 1
                    if result['status'] == 'PASSED':
                        passed_tests += 1
                    else:
                        failed_tests += 1
        
        report = {
            'test_suite': 'Integration Test Suite - Memory Optimization',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_duration_seconds': total_duration,
            'environment': {
                'python_version': sys.version,
                'platform': sys.platform,
                'airflow_root': str(self.airflow_root),
            },
            'test_results': self.test_results,
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'overall_status': 'PASSED' if failed_tests == 0 else 'FAILED'
            }
        }
        
        return report

def main():
    """Main execution function."""
    airflow_root = '/Users/<USER>/oss/pr/airflow'
    
    logger.info("🚀 Starting Integration Test Suite...")
    
    test_suite = IntegrationTestSuite(airflow_root)
    
    # Run integration tests
    test_suite.run_integration_tests()
    
    # Generate and save report
    report = test_suite.generate_report()
    
    # Save report to file
    report_file = Path(airflow_root) / 'integration_test_report.json'
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"📄 Report saved to: {report_file}")
    
    # Print summary
    summary = report['summary']
    logger.info(f"🎯 Test Summary: {summary['passed_tests']}/{summary['total_tests']} tests passed")
    logger.info(f"📈 Success Rate: {summary['success_rate']:.1f}%")
    logger.info(f"🏁 Overall Status: {summary['overall_status']}")
    
    return 0 if summary['overall_status'] == 'PASSED' else 1

if __name__ == '__main__':
    sys.exit(main())
