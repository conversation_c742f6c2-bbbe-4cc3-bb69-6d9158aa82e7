#!/usr/bin/env python3
"""
Breeze Memory Monitoring Report
Comprehensive memory analysis following Breeze testing patterns
"""

import os
import sys
import time
import json
import psutil
import gc
import subprocess
from pathlib import Path
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_memory_monitoring_test():
    """Run comprehensive memory monitoring test following Breeze patterns."""
    
    logger.info("🚀 Starting Breeze Memory Monitoring Test...")
    
    airflow_root = Path('/Users/<USER>/oss/pr/airflow')
    os.chdir(airflow_root)
    
    # Test results storage
    results = {
        'test_suite': 'Breeze Memory Monitoring Report',
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'environment': {
            'python_version': sys.version,
            'platform': sys.platform,
            'airflow_root': str(airflow_root),
        },
        'tests': {}
    }
    
    # Test 1: Factory Function Memory Efficiency
    logger.info("📊 Test 1: Factory Function Memory Efficiency...")
    
    try:
        # Set minimal environment
        os.environ.update({
            'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
            'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': 'sqlite:////tmp/airflow_test.db',
            'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
        })
        
        # Import factory functions
        sys.path.insert(0, str(airflow_root))
        from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
            _get_expected_k8s_dict,
            _get_expected_application_dict_with_labels
        )
        
        # Memory efficiency test
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024
        
        # Create many objects to test memory behavior
        objects = []
        for i in range(2000):
            k8s_dict = _get_expected_k8s_dict()
            app_dict = _get_expected_application_dict_with_labels(f'task_{i}')
            objects.append((k8s_dict, app_dict))
            
            # Monitor memory every 500 objects
            if i % 500 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                logger.info(f"   Objects created: {i+1}, Memory: {current_memory:.1f}MB")
        
        peak_memory = process.memory_info().rss / 1024 / 1024
        
        # Clear objects and force garbage collection
        objects.clear()
        gc.collect()
        
        end_memory = process.memory_info().rss / 1024 / 1024
        
        memory_increase = peak_memory - start_memory
        memory_recovered = peak_memory - end_memory
        recovery_percentage = (memory_recovered / memory_increase * 100) if memory_increase > 0 else 0
        
        results['tests']['memory_efficiency'] = {
            'start_memory_mb': start_memory,
            'peak_memory_mb': peak_memory,
            'end_memory_mb': end_memory,
            'memory_increase_mb': memory_increase,
            'memory_recovered_mb': memory_recovered,
            'recovery_percentage': recovery_percentage,
            'objects_created': 2000,
            'memory_per_object_kb': (memory_increase * 1024) / 2000,
            'status': 'PASSED' if recovery_percentage > 50 else 'FAILED'
        }
        
        logger.info(f"✅ Memory efficiency: {recovery_percentage:.1f}% recovery")
        
    except Exception as e:
        logger.error(f"❌ Memory efficiency test failed: {e}")
        results['tests']['memory_efficiency'] = {
            'error': str(e),
            'status': 'FAILED'
        }
    
    # Test 2: Performance Benchmarks
    logger.info("⚡ Test 2: Performance Benchmarks...")
    
    try:
        # Performance test
        iterations = 50000
        
        # Test k8s dict creation
        start_time = time.time()
        for i in range(iterations):
            k8s_dict = _get_expected_k8s_dict()
        k8s_duration = time.time() - start_time
        
        # Test app dict creation
        start_time = time.time()
        for i in range(iterations):
            app_dict = _get_expected_application_dict_with_labels(f'task_{i}')
        app_duration = time.time() - start_time
        
        total_calls = iterations * 2
        total_time = k8s_duration + app_duration
        calls_per_second = total_calls / total_time
        
        results['tests']['performance'] = {
            'k8s_dict_iterations': iterations,
            'k8s_dict_time_seconds': k8s_duration,
            'k8s_dict_time_per_call_ms': (k8s_duration / iterations) * 1000,
            'app_dict_iterations': iterations,
            'app_dict_time_seconds': app_duration,
            'app_dict_time_per_call_ms': (app_duration / iterations) * 1000,
            'total_calls': total_calls,
            'total_time_seconds': total_time,
            'calls_per_second': calls_per_second,
            'status': 'PASSED' if calls_per_second > 100000 else 'FAILED'
        }
        
        logger.info(f"✅ Performance: {calls_per_second:.0f} calls/sec")
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        results['tests']['performance'] = {
            'error': str(e),
            'status': 'FAILED'
        }
    
    # Test 3: Pytest Integration Test
    logger.info("🧪 Test 3: Pytest Integration Test...")
    
    try:
        # Run specific pytest tests
        cmd = [
            sys.executable, '-m', 'pytest',
            'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication',
            '-v',
            '--tb=short',
            '--disable-warnings'
        ]
        
        env = os.environ.copy()
        env.update({
            'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
            'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': 'sqlite:////tmp/airflow_test.db',
            'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
        })
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=airflow_root, env=env)
        duration = time.time() - start_time
        
        # Parse test results
        test_count = result.stdout.count('PASSED')
        failed_count = result.stdout.count('FAILED')
        
        results['tests']['pytest_integration'] = {
            'returncode': result.returncode,
            'duration_seconds': duration,
            'tests_passed': test_count,
            'tests_failed': failed_count,
            'total_tests': test_count + failed_count,
            'success_rate': (test_count / (test_count + failed_count) * 100) if (test_count + failed_count) > 0 else 0,
            'status': 'PASSED' if result.returncode == 0 else 'FAILED'
        }
        
        logger.info(f"✅ Pytest: {test_count} tests passed in {duration:.1f}s")
        
    except Exception as e:
        logger.error(f"❌ Pytest integration test failed: {e}")
        results['tests']['pytest_integration'] = {
            'error': str(e),
            'status': 'FAILED'
        }
    
    # Test 4: OOM Prevention Validation
    logger.info("🛡️  Test 4: OOM Prevention Validation...")
    
    try:
        # Simulate high memory usage scenario
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024
        
        # Create a large number of objects to test OOM prevention
        max_objects = 5000
        objects = []
        memory_samples = []
        
        for i in range(max_objects):
            k8s_dict = _get_expected_k8s_dict()
            app_dict = _get_expected_application_dict_with_labels(f'stress_test_{i}')
            objects.append((k8s_dict, app_dict))
            
            # Sample memory every 1000 objects
            if i % 1000 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
                logger.info(f"   Stress test: {i+1}/{max_objects} objects, Memory: {current_memory:.1f}MB")
        
        peak_memory = process.memory_info().rss / 1024 / 1024
        
        # Check if memory growth is linear (good) vs exponential (bad)
        memory_growth_rate = (peak_memory - start_memory) / max_objects
        
        # Clear and measure recovery
        objects.clear()
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024
        
        results['tests']['oom_prevention'] = {
            'start_memory_mb': start_memory,
            'peak_memory_mb': peak_memory,
            'final_memory_mb': final_memory,
            'objects_created': max_objects,
            'memory_growth_rate_kb_per_object': memory_growth_rate * 1024,
            'memory_samples': memory_samples,
            'linear_growth': memory_growth_rate < 0.01,  # Less than 10KB per object is good
            'status': 'PASSED' if memory_growth_rate < 0.01 else 'FAILED'
        }
        
        logger.info(f"✅ OOM Prevention: {memory_growth_rate*1024:.2f}KB per object")
        
    except Exception as e:
        logger.error(f"❌ OOM prevention test failed: {e}")
        results['tests']['oom_prevention'] = {
            'error': str(e),
            'status': 'FAILED'
        }
    
    # Calculate overall summary
    total_tests = len(results['tests'])
    passed_tests = sum(1 for test in results['tests'].values() if test.get('status') == 'PASSED')
    
    results['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': total_tests - passed_tests,
        'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
        'overall_status': 'PASSED' if passed_tests == total_tests else 'FAILED'
    }
    
    # Save report
    report_file = airflow_root / 'breeze_memory_monitoring_report.json'
    with open(report_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"📄 Report saved to: {report_file}")
    
    # Print summary
    logger.info(f"🎯 Summary: {passed_tests}/{total_tests} tests passed")
    logger.info(f"📈 Success Rate: {results['summary']['success_rate']:.1f}%")
    logger.info(f"🏁 Overall Status: {results['summary']['overall_status']}")
    
    return results

if __name__ == '__main__':
    results = run_memory_monitoring_test()
    sys.exit(0 if results['summary']['overall_status'] == 'PASSED' else 1)
