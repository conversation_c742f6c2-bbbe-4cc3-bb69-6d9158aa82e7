#!/usr/bin/env python3
"""
Unit Test Memory Monitor
Real-time memory monitoring during unit test execution
"""

import os
import sys
import time
import json
import psutil
import gc
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnitTestMemoryMonitor:
    """Monitor memory usage during unit test execution."""
    
    def __init__(self, airflow_root: str):
        self.airflow_root = Path(airflow_root)
        self.monitoring = False
        self.memory_samples = []
        self.start_time = time.time()
        
    def start_monitoring(self):
        """Start memory monitoring in background thread."""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_memory)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("📊 Memory monitoring started")
        
    def stop_monitoring(self):
        """Stop memory monitoring."""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=1)
        logger.info("📊 Memory monitoring stopped")
        
    def _monitor_memory(self):
        """Background memory monitoring loop."""
        while self.monitoring:
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                
                sample = {
                    'timestamp': time.time(),
                    'rss_mb': memory_info.rss / 1024 / 1024,
                    'vms_mb': memory_info.vms / 1024 / 1024,
                    'percent': process.memory_percent(),
                    'gc_count': sum(gc.get_count()),
                }
                
                self.memory_samples.append(sample)
                time.sleep(0.1)  # Sample every 100ms
                
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                break
                
    def run_unit_tests_with_monitoring(self) -> Dict[str, Any]:
        """Run unit tests with real-time memory monitoring."""
        logger.info("🧪 Running Unit Tests with Memory Monitoring...")
        
        results = {}
        
        # Test 1: Core Spark Kubernetes Tests
        logger.info("Running TestSparkKubernetesOperatorCreateApplication...")
        
        self.start_monitoring()
        
        cmd = [
            sys.executable, '-m', 'pytest',
            'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication',
            '-v',
            '--tb=short',
            '--disable-warnings',
            '--junit-xml=files/test_result-unit-memory-monitored.xml'
        ]
        
        env = os.environ.copy()
        env.update({
            'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
            'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': 'sqlite:////tmp/airflow_test.db',
            'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
        })
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root, env=env)
        duration = time.time() - start_time
        
        self.stop_monitoring()
        
        # Parse test results
        test_count = result.stdout.count('PASSED')
        failed_count = result.stdout.count('FAILED')
        
        # Analyze memory usage
        if self.memory_samples:
            memory_analysis = self._analyze_memory_samples()
        else:
            memory_analysis = {'error': 'No memory samples collected'}
        
        results['create_application_tests'] = {
            'returncode': result.returncode,
            'duration_seconds': duration,
            'tests_passed': test_count,
            'tests_failed': failed_count,
            'total_tests': test_count + failed_count,
            'success_rate': (test_count / (test_count + failed_count) * 100) if (test_count + failed_count) > 0 else 0,
            'memory_analysis': memory_analysis,
            'status': 'PASSED' if result.returncode == 0 else 'FAILED'
        }
        
        logger.info(f"✅ CreateApplication tests: {test_count} passed in {duration:.1f}s")
        
        # Test 2: Core Unit Tests
        logger.info("Running core unit tests...")
        
        self.memory_samples = []  # Reset for next test
        self.start_monitoring()
        
        cmd = [
            sys.executable, '-m', 'pytest',
            'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::test_spark_kubernetes_operator',
            'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::test_init_spark_kubernetes_operator',
            'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::test_spark_kubernetes_operator_hook',
            '-v',
            '--tb=short',
            '--disable-warnings',
            '--junit-xml=files/test_result-core-memory-monitored.xml'
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root, env=env)
        duration = time.time() - start_time
        
        self.stop_monitoring()
        
        # Parse test results
        test_count = result.stdout.count('PASSED')
        failed_count = result.stdout.count('FAILED')
        
        # Analyze memory usage
        if self.memory_samples:
            memory_analysis = self._analyze_memory_samples()
        else:
            memory_analysis = {'error': 'No memory samples collected'}
        
        results['core_unit_tests'] = {
            'returncode': result.returncode,
            'duration_seconds': duration,
            'tests_passed': test_count,
            'tests_failed': failed_count,
            'total_tests': test_count + failed_count,
            'success_rate': (test_count / (test_count + failed_count) * 100) if (test_count + failed_count) > 0 else 0,
            'memory_analysis': memory_analysis,
            'status': 'PASSED' if result.returncode == 0 else 'FAILED'
        }
        
        logger.info(f"✅ Core unit tests: {test_count} passed in {duration:.1f}s")
        
        # Test 3: Factory Function Direct Test
        logger.info("Running factory function direct test...")
        
        self.memory_samples = []  # Reset for next test
        self.start_monitoring()
        
        try:
            # Import and test factory functions directly
            sys.path.insert(0, str(self.airflow_root))
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test factory functions with multiple iterations
            start_time = time.time()
            
            for i in range(100):
                k8s_dict = _get_expected_k8s_dict()
                app_dict = _get_expected_application_dict_with_labels(f'test_task_{i}')
                
                # Validate structure
                assert k8s_dict['apiVersion'] == 'sparkoperator.k8s.io/v1beta2'
                assert k8s_dict['kind'] == 'SparkApplication'
                assert app_dict['spec']['driver']['labels']['task_id'] == f'test_task_{i}'
                assert app_dict['spec']['executor']['labels']['task_id'] == f'test_task_{i}'
            
            duration = time.time() - start_time
            
            self.stop_monitoring()
            
            # Analyze memory usage
            if self.memory_samples:
                memory_analysis = self._analyze_memory_samples()
            else:
                memory_analysis = {'error': 'No memory samples collected'}
            
            results['factory_function_test'] = {
                'duration_seconds': duration,
                'iterations': 100,
                'objects_created': 200,  # 100 k8s_dict + 100 app_dict
                'memory_analysis': memory_analysis,
                'status': 'PASSED'
            }
            
            logger.info(f"✅ Factory function test: 100 iterations in {duration:.3f}s")
            
        except Exception as e:
            self.stop_monitoring()
            logger.error(f"❌ Factory function test failed: {e}")
            results['factory_function_test'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def _analyze_memory_samples(self) -> Dict[str, Any]:
        """Analyze collected memory samples."""
        if not self.memory_samples:
            return {'error': 'No memory samples to analyze'}
        
        rss_values = [sample['rss_mb'] for sample in self.memory_samples]
        vms_values = [sample['vms_mb'] for sample in self.memory_samples]
        gc_values = [sample['gc_count'] for sample in self.memory_samples]
        
        analysis = {
            'sample_count': len(self.memory_samples),
            'duration_seconds': self.memory_samples[-1]['timestamp'] - self.memory_samples[0]['timestamp'],
            'rss_memory': {
                'start_mb': rss_values[0],
                'peak_mb': max(rss_values),
                'end_mb': rss_values[-1],
                'min_mb': min(rss_values),
                'avg_mb': sum(rss_values) / len(rss_values),
                'delta_mb': rss_values[-1] - rss_values[0],
                'peak_increase_mb': max(rss_values) - rss_values[0],
            },
            'vms_memory': {
                'start_mb': vms_values[0],
                'peak_mb': max(vms_values),
                'end_mb': vms_values[-1],
                'delta_mb': vms_values[-1] - vms_values[0],
            },
            'garbage_collection': {
                'start_count': gc_values[0],
                'end_count': gc_values[-1],
                'total_collections': gc_values[-1] - gc_values[0],
            },
            'memory_efficiency': {
                'peak_recovery_percentage': ((max(rss_values) - rss_values[-1]) / (max(rss_values) - rss_values[0]) * 100) if (max(rss_values) - rss_values[0]) > 0 else 0,
                'stable_memory': abs(rss_values[-1] - rss_values[0]) < 5,  # Less than 5MB change
                'no_memory_leaks': rss_values[-1] <= rss_values[0] + 2,  # Allow 2MB growth
            }
        }
        
        return analysis
    
    def generate_report(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive unit test report."""
        logger.info("📋 Generating Unit Test Memory Report...")
        
        total_duration = time.time() - self.start_time
        
        # Calculate overall statistics
        total_tests = 0
        passed_tests = 0
        
        for test_name, result in test_results.items():
            if 'total_tests' in result:
                total_tests += result['total_tests']
                passed_tests += result['tests_passed']
        
        report = {
            'test_suite': 'Unit Test Memory Monitoring Report',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_duration_seconds': total_duration,
            'environment': {
                'python_version': sys.version,
                'platform': sys.platform,
                'airflow_root': str(self.airflow_root),
            },
            'test_results': test_results,
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'overall_status': 'PASSED' if all(result.get('status') == 'PASSED' for result in test_results.values()) else 'FAILED'
            }
        }
        
        return report

def main():
    """Main execution function."""
    airflow_root = '/Users/<USER>/oss/pr/airflow'
    
    logger.info("🚀 Starting Unit Test Memory Monitoring...")
    
    monitor = UnitTestMemoryMonitor(airflow_root)
    
    # Run tests with monitoring
    test_results = monitor.run_unit_tests_with_monitoring()
    
    # Generate and save report
    report = monitor.generate_report(test_results)
    
    # Save report to file
    report_file = Path(airflow_root) / 'unit_test_memory_report.json'
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"📄 Report saved to: {report_file}")
    
    # Print summary
    summary = report['summary']
    logger.info(f"🎯 Test Summary: {summary['passed_tests']}/{summary['total_tests']} tests passed")
    logger.info(f"📈 Success Rate: {summary['success_rate']:.1f}%")
    logger.info(f"🏁 Overall Status: {summary['overall_status']}")
    
    return 0 if summary['overall_status'] == 'PASSED' else 1

if __name__ == '__main__':
    sys.exit(main())
