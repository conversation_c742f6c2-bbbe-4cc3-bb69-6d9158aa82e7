#!/usr/bin/env python3
"""
Focused Integration Test Suite
Targeted integration tests for memory optimization validation
"""

import os
import sys
import time
import json
import psutil
import gc
import subprocess
from pathlib import Path
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FocusedIntegrationTest:
    """Focused integration test for memory optimization."""
    
    def __init__(self, airflow_root: str):
        self.airflow_root = Path(airflow_root)
        self.start_time = time.time()
        
    def test_specific_k8s_operators(self) -> Dict[str, Any]:
        """Test specific Kubernetes operators that are most likely to interact with our changes."""
        logger.info("🔗 Testing Specific K8S Operator Integration...")
        
        results = {}
        
        # Test 1: Pod Operator (closely related to Spark)
        logger.info("Testing Pod Operator integration...")
        
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_pod.py',
                '-v',
                '--tb=short',
                '--disable-warnings',
                '--maxfail=5',
                '--junit-xml=files/test_result-pod-integration.xml'
            ]
            
            env = os.environ.copy()
            env.update({
                'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
                'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': 'sqlite:////tmp/airflow_test.db',
                'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
            })
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root, env=env, timeout=120)
            duration = time.time() - start_time
            
            test_count = result.stdout.count('PASSED')
            failed_count = result.stdout.count('FAILED')
            error_count = result.stdout.count('ERROR')
            
            results['pod_operator'] = {
                'returncode': result.returncode,
                'duration_seconds': duration,
                'tests_passed': test_count,
                'tests_failed': failed_count,
                'tests_error': error_count,
                'total_tests': test_count + failed_count + error_count,
                'success_rate': (test_count / (test_count + failed_count + error_count) * 100) if (test_count + failed_count + error_count) > 0 else 0,
                'status': 'PASSED' if result.returncode == 0 else 'FAILED'
            }
            
            logger.info(f"✅ Pod operator: {test_count} passed, {failed_count} failed")
            
        except subprocess.TimeoutExpired:
            results['pod_operator'] = {'error': 'Timeout', 'status': 'TIMEOUT'}
        except Exception as e:
            results['pod_operator'] = {'error': str(e), 'status': 'FAILED'}
        
        # Test 2: Job Operator (similar patterns)
        logger.info("Testing Job Operator integration...")
        
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_job.py',
                '-v',
                '--tb=short',
                '--disable-warnings',
                '--maxfail=5',
                '--junit-xml=files/test_result-job-integration.xml'
            ]
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root, env=env, timeout=120)
            duration = time.time() - start_time
            
            test_count = result.stdout.count('PASSED')
            failed_count = result.stdout.count('FAILED')
            error_count = result.stdout.count('ERROR')
            
            results['job_operator'] = {
                'returncode': result.returncode,
                'duration_seconds': duration,
                'tests_passed': test_count,
                'tests_failed': failed_count,
                'tests_error': error_count,
                'total_tests': test_count + failed_count + error_count,
                'success_rate': (test_count / (test_count + failed_count + error_count) * 100) if (test_count + failed_count + error_count) > 0 else 0,
                'status': 'PASSED' if result.returncode == 0 else 'FAILED'
            }
            
            logger.info(f"✅ Job operator: {test_count} passed, {failed_count} failed")
            
        except subprocess.TimeoutExpired:
            results['job_operator'] = {'error': 'Timeout', 'status': 'TIMEOUT'}
        except Exception as e:
            results['job_operator'] = {'error': str(e), 'status': 'FAILED'}
        
        return results
    
    def test_memory_cross_component(self) -> Dict[str, Any]:
        """Test memory behavior across components."""
        logger.info("📊 Testing Cross-Component Memory Behavior...")
        
        results = {}
        
        try:
            # Import our factory functions
            sys.path.insert(0, str(self.airflow_root))
            
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test 1: Memory isolation between components
            logger.info("Testing memory isolation...")
            
            process = psutil.Process()
            start_memory = process.memory_info().rss / 1024 / 1024
            
            # Simulate multiple components using factory functions
            component_a_objects = []
            component_b_objects = []
            
            for i in range(200):
                # Component A creates objects
                k8s_dict_a = _get_expected_k8s_dict()
                app_dict_a = _get_expected_application_dict_with_labels(f'component_a_{i}')
                component_a_objects.append((k8s_dict_a, app_dict_a))
                
                # Component B creates objects
                k8s_dict_b = _get_expected_k8s_dict()
                app_dict_b = _get_expected_application_dict_with_labels(f'component_b_{i}')
                component_b_objects.append((k8s_dict_b, app_dict_b))
            
            mid_memory = process.memory_info().rss / 1024 / 1024
            
            # Component A finishes and clears its objects
            component_a_objects.clear()
            gc.collect()
            
            after_a_clear = process.memory_info().rss / 1024 / 1024
            
            # Component B finishes and clears its objects
            component_b_objects.clear()
            gc.collect()
            
            end_memory = process.memory_info().rss / 1024 / 1024
            
            results['memory_isolation'] = {
                'start_memory_mb': start_memory,
                'peak_memory_mb': mid_memory,
                'after_component_a_clear_mb': after_a_clear,
                'end_memory_mb': end_memory,
                'component_a_recovery_mb': mid_memory - after_a_clear,
                'component_b_recovery_mb': after_a_clear - end_memory,
                'total_recovery_mb': mid_memory - end_memory,
                'objects_per_component': 400,  # 200 * 2 objects each
                'isolation_effective': (mid_memory - after_a_clear) > 0,
                'status': 'PASSED'
            }
            
            logger.info(f"✅ Memory isolation: {mid_memory - end_memory:.2f}MB recovered")
            
            # Test 2: Factory function consistency
            logger.info("Testing factory function consistency...")
            
            # Create multiple objects and verify they're independent
            objects = []
            task_ids = []
            
            for i in range(100):
                task_id = f'consistency_test_{i}'
                app_dict = _get_expected_application_dict_with_labels(task_id)
                
                # Verify task_id is correctly set
                assert app_dict['spec']['driver']['labels']['task_id'] == task_id
                assert app_dict['spec']['executor']['labels']['task_id'] == task_id
                
                objects.append(app_dict)
                task_ids.append(task_id)
            
            # Verify all objects are independent (different task_ids)
            unique_task_ids = set()
            for obj in objects:
                driver_task_id = obj['spec']['driver']['labels']['task_id']
                executor_task_id = obj['spec']['executor']['labels']['task_id']
                
                assert driver_task_id == executor_task_id
                unique_task_ids.add(driver_task_id)
            
            results['factory_consistency'] = {
                'objects_created': len(objects),
                'unique_task_ids': len(unique_task_ids),
                'all_task_ids_unique': len(unique_task_ids) == len(objects),
                'task_id_propagation_correct': True,
                'objects_independent': True,
                'status': 'PASSED'
            }
            
            logger.info(f"✅ Factory consistency: {len(unique_task_ids)} unique objects")
            
            # Clean up
            objects.clear()
            gc.collect()
            
        except Exception as e:
            logger.error(f"❌ Cross-component memory test failed: {e}")
            results['cross_component_error'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def test_import_compatibility(self) -> Dict[str, Any]:
        """Test import compatibility with existing code."""
        logger.info("🔌 Testing Import Compatibility...")
        
        results = {}
        
        try:
            # Test that our changes don't break imports
            logger.info("Testing import compatibility...")
            
            # Test direct imports
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test that functions are callable
            k8s_dict = _get_expected_k8s_dict()
            app_dict = _get_expected_application_dict_with_labels('import_test')
            
            # Test return types
            assert isinstance(k8s_dict, dict)
            assert isinstance(app_dict, dict)
            
            # Test structure
            assert 'apiVersion' in k8s_dict
            assert 'kind' in k8s_dict
            assert 'metadata' in k8s_dict
            assert 'spec' in k8s_dict
            
            assert 'apiVersion' in app_dict
            assert 'kind' in app_dict
            assert 'metadata' in app_dict
            assert 'spec' in app_dict
            
            # Test task_id propagation
            assert app_dict['spec']['driver']['labels']['task_id'] == 'import_test'
            assert app_dict['spec']['executor']['labels']['task_id'] == 'import_test'
            
            results['import_compatibility'] = {
                'functions_importable': True,
                'functions_callable': True,
                'return_types_correct': True,
                'structure_valid': True,
                'task_id_propagation': True,
                'backward_compatible': True,
                'status': 'PASSED'
            }
            
            logger.info("✅ Import compatibility: All tests passed")
            
        except Exception as e:
            logger.error(f"❌ Import compatibility test failed: {e}")
            results['import_compatibility'] = {
                'error': str(e),
                'status': 'FAILED'
            }
        
        return results
    
    def run_focused_tests(self) -> Dict[str, Any]:
        """Run all focused integration tests."""
        logger.info("🚀 Starting Focused Integration Tests...")
        
        results = {}
        
        # Set environment
        os.chdir(self.airflow_root)
        os.environ.update({
            'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
            'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': 'sqlite:////tmp/airflow_test.db',
            'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
            'PYTHONPATH': str(self.airflow_root),
        })
        
        # Run test categories
        results['k8s_operators'] = self.test_specific_k8s_operators()
        results['memory_behavior'] = self.test_memory_cross_component()
        results['compatibility'] = self.test_import_compatibility()
        
        return results
    
    def generate_report(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate focused integration test report."""
        logger.info("📋 Generating Focused Integration Report...")
        
        total_duration = time.time() - self.start_time
        
        # Calculate statistics
        total_tests = 0
        passed_tests = 0
        
        for category, tests in test_results.items():
            for test_name, result in tests.items():
                if isinstance(result, dict) and 'status' in result:
                    total_tests += 1
                    if result['status'] == 'PASSED':
                        passed_tests += 1
        
        report = {
            'test_suite': 'Focused Integration Test Suite',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_duration_seconds': total_duration,
            'test_results': test_results,
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'overall_status': 'PASSED' if passed_tests == total_tests else 'FAILED'
            }
        }
        
        return report

def main():
    """Main execution function."""
    airflow_root = '/Users/<USER>/oss/pr/airflow'
    
    logger.info("🚀 Starting Focused Integration Test Suite...")
    
    test_suite = FocusedIntegrationTest(airflow_root)
    
    # Run tests
    test_results = test_suite.run_focused_tests()
    
    # Generate report
    report = test_suite.generate_report(test_results)
    
    # Save report
    report_file = Path(airflow_root) / 'focused_integration_report.json'
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"📄 Report saved to: {report_file}")
    
    # Print summary
    summary = report['summary']
    logger.info(f"🎯 Test Summary: {summary['passed_tests']}/{summary['total_tests']} tests passed")
    logger.info(f"📈 Success Rate: {summary['success_rate']:.1f}%")
    logger.info(f"🏁 Overall Status: {summary['overall_status']}")
    
    return 0 if summary['overall_status'] == 'PASSED' else 1

if __name__ == '__main__':
    sys.exit(main())
