#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, '/Users/<USER>/oss/pr/airflow/providers/cncf/kubernetes/src')
sys.path.insert(0, '/Users/<USER>/oss/pr/airflow/airflow-core/src')

import yaml
from unittest import mock
from airflow.providers.cncf.kubernetes.operators.spark_kubernetes import SparkKubernetesOperator

# Create a simple test to verify the task context labels functionality
def test_task_context_labels():
    print("Testing SparkKubernetesOperator task context labels functionality...")
    
    # Create a minimal job spec
    job_spec = {
        'spark': {
            'apiVersion': 'sparkoperator.k8s.io/v1beta2',
            'kind': 'SparkApplication',
            'metadata': {'name': 'test-app'},
            'spec': {
                'driver': {'labels': {}},
                'executor': {'labels': {}}
            }
        }
    }
    
    # Create operator with reattach_on_restart=True
    op = SparkKubernetesOperator(
        template_spec=job_spec,
        kubernetes_conn_id='kubernetes_default_kube_config',
        task_id='test_task',
        reattach_on_restart=True
    )
    
    # Create a mock context
    mock_context = {
        'dag': mock.MagicMock(),
        'run_id': 'manual__2023-01-01T00:00:00+00:00',
        'ti': mock.MagicMock(),
        'task_instance': mock.MagicMock()
    }
    mock_context['dag'].dag_id = 'test_dag'
    mock_context['ti'].dag_id = 'test_dag'
    mock_context['ti'].task_id = 'test_task'
    mock_context['ti'].try_number = 1
    
    # Test the _get_ti_pod_labels method
    labels = op._get_ti_pod_labels(mock_context)
    print(f"Generated task context labels: {labels}")
    
    # Verify expected labels are present
    expected_labels = ['dag_id', 'task_id', 'run_id', 'spark_kubernetes_operator', 'try_number']
    for label in expected_labels:
        if label in labels:
            print(f"✓ Found expected label: {label} = {labels[label]}")
        else:
            print(f"✗ Missing expected label: {label}")
    
    print("Test completed successfully!")
    return True

if __name__ == '__main__':
    test_task_context_labels()