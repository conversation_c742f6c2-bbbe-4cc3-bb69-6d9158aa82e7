#!/usr/bin/env python3
"""
Memory monitoring script for Apache Airflow test optimization.

This script monitors memory usage during test execution to validate
memory optimizations and compare before/after results.

Usage:
    python memory_monitor.py --test-file test_spark_kubernetes.py
    python memory_monitor.py --test-method test_env
    python memory_monitor.py --baseline  # Run baseline memory test
"""

import argparse
import gc
import os
import psutil
import subprocess
import sys
import time
from contextlib import contextmanager
from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass
class MemorySnapshot:
    """Memory usage snapshot."""
    timestamp: float
    rss_mb: float  # Resident Set Size in MB
    vms_mb: float  # Virtual Memory Size in MB
    percent: float  # Memory percentage
    available_mb: float  # Available system memory in MB


@dataclass
class MemoryReport:
    """Memory usage report for a test run."""
    test_name: str
    start_memory: MemorySnapshot
    peak_memory: MemorySnapshot
    end_memory: MemorySnapshot
    duration_seconds: float
    memory_delta_mb: float
    peak_delta_mb: float


class MemoryMonitor:
    """Monitor memory usage during test execution."""
    
    def __init__(self, sample_interval: float = 0.1):
        self.sample_interval = sample_interval
        self.snapshots: List[MemorySnapshot] = []
        self.process = psutil.Process()
        
    def take_snapshot(self) -> MemorySnapshot:
        """Take a memory usage snapshot."""
        memory_info = self.process.memory_info()
        system_memory = psutil.virtual_memory()
        
        return MemorySnapshot(
            timestamp=time.time(),
            rss_mb=memory_info.rss / 1024 / 1024,
            vms_mb=memory_info.vms / 1024 / 1024,
            percent=self.process.memory_percent(),
            available_mb=system_memory.available / 1024 / 1024
        )
    
    @contextmanager
    def monitor_test(self, test_name: str):
        """Context manager to monitor memory during test execution."""
        # Force garbage collection before starting
        gc.collect()
        
        start_time = time.time()
        start_memory = self.take_snapshot()
        self.snapshots = [start_memory]
        
        try:
            yield self
        finally:
            # Force garbage collection after test
            gc.collect()
            time.sleep(0.1)  # Allow GC to complete
            
            end_time = time.time()
            end_memory = self.take_snapshot()
            
            # Find peak memory usage
            peak_memory = max(self.snapshots, key=lambda s: s.rss_mb)
            
            # Create report
            report = MemoryReport(
                test_name=test_name,
                start_memory=start_memory,
                peak_memory=peak_memory,
                end_memory=end_memory,
                duration_seconds=end_time - start_time,
                memory_delta_mb=end_memory.rss_mb - start_memory.rss_mb,
                peak_delta_mb=peak_memory.rss_mb - start_memory.rss_mb
            )
            
            self.print_report(report)
    
    def sample_memory(self):
        """Sample memory usage (call periodically during test)."""
        snapshot = self.take_snapshot()
        self.snapshots.append(snapshot)
    
    def print_report(self, report: MemoryReport):
        """Print memory usage report."""
        print(f"\n{'='*60}")
        print(f"MEMORY USAGE REPORT: {report.test_name}")
        print(f"{'='*60}")
        print(f"Duration: {report.duration_seconds:.2f} seconds")
        print(f"Start Memory: {report.start_memory.rss_mb:.1f} MB")
        print(f"Peak Memory:  {report.peak_memory.rss_mb:.1f} MB (+{report.peak_delta_mb:.1f} MB)")
        print(f"End Memory:   {report.end_memory.rss_mb:.1f} MB ({report.memory_delta_mb:+.1f} MB)")
        print(f"Memory Efficiency: {'GOOD' if report.memory_delta_mb < 10 else 'NEEDS_OPTIMIZATION'}")
        print(f"Available System Memory: {report.end_memory.available_mb:.1f} MB")
        print(f"{'='*60}\n")


def run_test_with_monitoring(test_command: List[str], test_name: str) -> MemoryReport:
    """Run a test command while monitoring memory usage."""
    monitor = MemoryMonitor()
    
    with monitor.monitor_test(test_name):
        # Start the test process
        process = subprocess.Popen(
            test_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Monitor memory while test runs
        while process.poll() is None:
            monitor.sample_memory()
            time.sleep(monitor.sample_interval)
        
        # Get test results
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            print(f"Test failed with return code {process.returncode}")
            print(f"STDOUT:\n{stdout}")
            print(f"STDERR:\n{stderr}")
        else:
            print(f"Test passed successfully")
    
    return monitor.snapshots[-1] if monitor.snapshots else None


def run_baseline_test():
    """Run baseline memory test to establish reference."""
    print("Running baseline memory test...")
    
    # Simple memory allocation test
    monitor = MemoryMonitor()
    
    with monitor.monitor_test("baseline_allocation"):
        # Simulate test data creation
        test_data = []
        for i in range(1000):
            # Simulate creating test dictionaries
            data = {
                "apiVersion": "sparkoperator.k8s.io/v1beta2",
                "kind": "SparkApplication",
                "metadata": {"name": f"test-{i}", "namespace": "default"},
                "spec": {"driver": {"cores": 1, "memory": "512m"}},
            }
            test_data.append(data)
            
            if i % 100 == 0:
                monitor.sample_memory()
        
        # Clear data
        test_data.clear()
        gc.collect()


def main():
    parser = argparse.ArgumentParser(description="Monitor memory usage during Airflow tests")
    parser.add_argument("--test-file", help="Test file to run")
    parser.add_argument("--test-method", help="Specific test method to run")
    parser.add_argument("--baseline", action="store_true", help="Run baseline memory test")
    parser.add_argument("--pytest-args", help="Additional pytest arguments")
    
    args = parser.parse_args()
    
    if args.baseline:
        run_baseline_test()
        return
    
    if not args.test_file and not args.test_method:
        print("Please specify --test-file, --test-method, or --baseline")
        sys.exit(1)
    
    # Build pytest command
    pytest_cmd = ["python", "-m", "pytest", "-v", "-s"]
    
    if args.pytest_args:
        pytest_cmd.extend(args.pytest_args.split())
    
    if args.test_file:
        pytest_cmd.append(args.test_file)
    
    if args.test_method:
        if args.test_file:
            pytest_cmd[-1] += f"::{args.test_method}"
        else:
            pytest_cmd.append(f"::{args.test_method}")
    
    test_name = args.test_method or args.test_file or "unknown"
    
    print(f"Running memory-monitored test: {' '.join(pytest_cmd)}")
    run_test_with_monitoring(pytest_cmd, test_name)


if __name__ == "__main__":
    main()
