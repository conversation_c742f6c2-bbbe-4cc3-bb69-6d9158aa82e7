{"test_suite": "Integration Test Suite - Memory Optimization", "timestamp": "2025-05-31 01:59:30", "total_duration_seconds": 303.0328209400177, "environment": {"python_version": "3.12.2 (v3.12.2:6abddd9f6a, Feb  6 2024, 17:02:06) [Clang 13.0.0 (clang-1300.0.29.30)]", "platform": "darwin", "airflow_root": "/Users/<USER>/oss/pr/airflow"}, "test_results": {"kubernetes_provider": {"kubernetes_operators": {"error": "Test execution timed out after 300 seconds", "status": "TIMEOUT"}}, "memory_integration": {"cross_component_memory": {"start_memory_mb": 191.796875, "peak_memory_mb": 196.671875, "end_memory_mb": 193.671875, "memory_increase_mb": 4.875, "memory_recovered_mb": 3.0, "recovery_percentage": 61.53846153846154, "objects_created": 1500, "components_simulated": 2, "status": "FAILED"}, "concurrent_access": {"start_memory_mb": 193.671875, "peak_memory_mb": 195.078125, "end_memory_mb": 195.078125, "memory_increase_mb": 1.40625, "memory_recovered_mb": 0.0, "recovery_percentage": 0.0, "threads_simulated": 10, "tasks_per_thread": 50, "total_objects": 1000, "status": "FAILED"}}, "api_compatibility": {"function_signatures": {"k8s_dict_callable": true, "app_dict_callable": true, "task_id_parameter_works": true, "return_types_correct": true, "status": "PASSED"}, "data_structure": {"k8s_dict_keys_match": true, "app_dict_keys_match": true, "k8s_dict_structure_valid": true, "app_dict_structure_valid": true, "task_id_propagation": true, "status": "PASSED"}}}, "summary": {"total_tests": 5, "passed_tests": 2, "failed_tests": 3, "success_rate": 40.0, "overall_status": "FAILED"}}