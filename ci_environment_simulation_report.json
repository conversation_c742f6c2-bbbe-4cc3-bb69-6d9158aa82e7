{"timestamp": "2025-05-30T22:36:20.520216", "overall_statistics": {"total_scenarios": 6, "successful_scenarios": 5, "scenario_success_rate": 0.8333333333333334, "average_test_success_rate": 0.9444444444444445, "average_memory_efficiency": 0.9952763671875, "oom_free_scenarios": 6, "oom_free_rate": 1.0, "resource_compliant_scenarios": 6, "resource_compliance_rate": 1.0, "ci_compatibility_distribution": {"EXCELLENT": 5, "POOR": 1}}, "scenario_results": [{"simulation_name": "GitHub Actions Standard", "memory_limit_mb": 7000, "parallel_workers": 1, "test_count": 3, "start_time": 1748624732.8852181, "end_time": 1748624742.360165, "duration_seconds": 9.474946975708008, "peak_memory_mb": 18.03125, "memory_efficiency": 0.9974241071428571, "tests_passed": 3, "tests_failed": 0, "success_rate": 1.0, "oom_occurred": false, "resource_constraints_met": true, "ci_compatibility": "EXCELLENT"}, {"simulation_name": "GitHub Actions Constrained", "memory_limit_mb": 3500, "parallel_workers": 1, "test_count": 3, "start_time": 1748624742.3602061, "end_time": 1748624751.586282, "duration_seconds": 9.226075887680054, "peak_memory_mb": 18.109375, "memory_efficiency": 0.9948258928571428, "tests_passed": 3, "tests_failed": 0, "success_rate": 1.0, "oom_occurred": false, "resource_constraints_met": true, "ci_compatibility": "EXCELLENT"}, {"simulation_name": "CircleCI Medium", "memory_limit_mb": 4000, "parallel_workers": 2, "test_count": 3, "start_time": 1748624751.586383, "end_time": 1748624757.5556648, "duration_seconds": 5.9692816734313965, "peak_memory_mb": 16.84375, "memory_efficiency": 0.9957890625, "tests_passed": 3, "tests_failed": 0, "success_rate": 1.0, "oom_occurred": false, "resource_constraints_met": true, "ci_compatibility": "EXCELLENT"}, {"simulation_name": "Travis CI Standard", "memory_limit_mb": 3000, "parallel_workers": 1, "test_count": 3, "start_time": 1748624757.5557408, "end_time": 1748624766.904578, "duration_seconds": 9.34883713722229, "peak_memory_mb": 16.921875, "memory_efficiency": 0.994359375, "tests_passed": 3, "tests_failed": 0, "success_rate": 1.0, "oom_occurred": false, "resource_constraints_met": true, "ci_compatibility": "EXCELLENT"}, {"simulation_name": "<PERSON>", "memory_limit_mb": 8000, "parallel_workers": 3, "test_count": 3, "start_time": 1748624766.9046059, "end_time": 1748624771.529103, "duration_seconds": 4.624497175216675, "peak_memory_mb": 17.171875, "memory_efficiency": 0.997853515625, "tests_passed": 2, "tests_failed": 1, "success_rate": 0.6666666666666666, "oom_occurred": false, "resource_constraints_met": true, "ci_compatibility": "POOR"}, {"simulation_name": "Minimal CI Environment", "memory_limit_mb": 2000, "parallel_workers": 1, "test_count": 3, "start_time": 1748624771.5291672, "end_time": 1748624780.520111, "duration_seconds": 8.990943908691406, "peak_memory_mb": 17.1875, "memory_efficiency": 0.99140625, "tests_passed": 3, "tests_failed": 0, "success_rate": 1.0, "oom_occurred": false, "resource_constraints_met": true, "ci_compatibility": "EXCELLENT"}]}