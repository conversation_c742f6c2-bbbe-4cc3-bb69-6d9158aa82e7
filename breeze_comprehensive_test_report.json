{"test_suite": "Breeze-Compatible Comprehensive Test Suite", "timestamp": "2025-05-31 02:23:12", "total_duration_seconds": 471.02615189552307, "environment": {"python_version": "3.12.2 (v3.12.2:6abddd9f6a, Feb  6 2024, 17:02:06) [Clang 13.0.0 (clang-1300.0.29.30)]", "platform": "darwin", "airflow_root": "/Users/<USER>/oss/pr/airflow", "breeze_compatible": true, "ci_simulation": true}, "test_results": {"unit_tests": {"breeze_unit_tests": {"returncode": -9, "duration_seconds": 432.7077748775482, "tests_passed": 19, "tests_failed": 1, "tests_error": 0, "total_tests": 20, "success_rate": 95.0, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.12.2, pytest-8.3.5, pluggy-1.6.0 -- /Users/<USER>/oss/pr/airflow/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/oss/pr/airflow\nconfigfile: pyproject.toml\nplugins: instafail-0.5.0, anyio-4.9.0, timeouts-1.2.1, time-machine-2.16.0, custom-exit-code-0.3.0, icdiff-0.9, rerunfailures-15.1, kgb-7.2, asyncio-0.25.0, mock-3.14.0, unordered-0.6.1, cov-6.1.1, xdist-3.6.1, requests-mock-1.12.1\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=function\nsetup timeout: 0.0s, execution timeout: 0.0s, teardown timeout: 0.0s\ncollecting ... collected 40 items\n\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::test_spark_kubernetes_operator PASSED [  2%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::test_init_spark_kubernetes_operator PASSED [  5%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::test_spark_kubernetes_operator_hook PASSED [  7%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application[True-default_yaml-spark/application_test.yaml] PASSED [ 10%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application[True-default_json-spark/application_test.json] PASSED [ 12%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application[False-default_yaml-spark/application_test.yaml] PASSED [ 15%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application[False-default_json-spark/application_test.json] PASSED [ 17%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_from_operator_args[True-default_yaml-spark/application_test.yaml] PASSED [ 20%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_from_operator_args[True-default_json-spark/application_test.json] PASSED [ 22%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_from_operator_args[False-default_yaml-spark/application_test.yaml] PASSED [ 25%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_from_operator_args[False-default_json-spark/application_test.json] PASSED [ 27%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_task_id[True-task_id_yml-spark/application_test_with_no_name_from_config.yaml] PASSED [ 30%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_task_id[True-task_id_json-spark/application_test_with_no_name_from_config.json] PASSED [ 32%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_task_id[False-task_id_yml-spark/application_test_with_no_name_from_config.yaml] PASSED [ 35%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_create_application_and_use_name_task_id[False-task_id_json-spark/application_test_with_no_name_from_config.json] PASSED [ 37%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_new_template_from_yaml[True] PASSED [ 40%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_new_template_from_yaml[False] PASSED [ 42%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_template_spec[True] PASSED [ 45%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperatorCreateApplication::test_template_spec[False] PASSED [ 47%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperator::test_env_placeholder FAILED [ 50%]\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py::TestSparkKubernetesOperator::test_volume ", "stderr": "", "status": "FAILED"}}, "integration_tests": {"breeze_integration_tests": {"returncode": 0, "duration_seconds": 33.66078805923462, "tests_passed": 168, "tests_failed": 0, "tests_error": 0, "total_tests": 168, "success_rate": 100.0, "status": "PASSED"}}, "memory_validation": {"breeze_memory_validation": {"start_memory_mb": 192.328125, "peak_memory_mb": 195.609375, "end_memory_mb": 195.609375, "memory_increase_mb": 3.28125, "memory_recovered_mb": 0.0, "recovery_percentage": 0.0, "test_runs": 10, "test_cases_per_run": 50, "total_objects": 1000, "memory_per_object_kb": 3.36, "breeze_memory_efficient": true, "breeze_recovery_good": false, "status": "FAILED"}}, "ci_simulation": {"breeze_ci_simulation": {"returncode": 0, "duration_seconds": 3.3920469284057617, "tests_passed": 16, "tests_failed": 0, "total_tests": 16, "success_rate": 100.0, "execution_time_acceptable": true, "breeze_ci_compatible": true, "status": "PASSED"}}}, "memory_metrics": {"final_memory_mb": 194.609375, "memory_percent": 1.055823432074653, "gc_collections": 28}, "summary": {"total_tests": 4, "passed_tests": 2, "failed_tests": 2, "success_rate": 50.0, "overall_status": "FAILED", "breeze_compatible": true}}