{"test_suite": "Unit Test Memory Monitoring Report", "timestamp": "2025-05-31 01:44:27", "total_duration_seconds": 8.264147996902466, "environment": {"python_version": "3.12.2 (v3.12.2:6abddd9f6a, Feb  6 2024, 17:02:06) [Clang 13.0.0 (clang-1300.0.29.30)]", "platform": "darwin", "airflow_root": "/Users/<USER>/oss/pr/airflow"}, "test_results": {"create_application_tests": {"returncode": 0, "duration_seconds": 3.9461779594421387, "tests_passed": 16, "tests_failed": 0, "total_tests": 16, "success_rate": 100.0, "memory_analysis": {"sample_count": 38, "duration_seconds": 3.8665621280670166, "rss_memory": {"start_mb": 16.90625, "peak_mb": 17.03125, "end_mb": 17.03125, "min_mb": 16.90625, "avg_mb": 16.98889802631579, "delta_mb": 0.125, "peak_increase_mb": 0.125}, "vms_memory": {"start_mb": 401151.328125, "peak_mb": 401152.328125, "end_mb": 401152.328125, "delta_mb": 1.0}, "garbage_collection": {"start_count": 601, "end_count": 701, "total_collections": 100}, "memory_efficiency": {"peak_recovery_percentage": 0.0, "stable_memory": true, "no_memory_leaks": true}}, "status": "PASSED"}, "core_unit_tests": {"returncode": 0, "duration_seconds": 3.260140895843506, "tests_passed": 3, "tests_failed": 0, "total_tests": 3, "success_rate": 100.0, "memory_analysis": {"sample_count": 32, "duration_seconds": 3.228877067565918, "rss_memory": {"start_mb": 17.03125, "peak_mb": 17.109375, "end_mb": 17.109375, "min_mb": 17.03125, "avg_mb": 17.0986328125, "delta_mb": 0.078125, "peak_increase_mb": 0.078125}, "vms_memory": {"start_mb": 401152.328125, "peak_mb": 401280.328125, "end_mb": 401280.328125, "delta_mb": 128.0}, "garbage_collection": {"start_count": 682, "end_count": 704, "total_collections": 22}, "memory_efficiency": {"peak_recovery_percentage": 0.0, "stable_memory": true, "no_memory_leaks": true}}, "status": "PASSED"}, "factory_function_test": {"duration_seconds": 0.00021600723266601562, "iterations": 100, "objects_created": 200, "memory_analysis": {"sample_count": 9, "duration_seconds": 0.8563723564147949, "rss_memory": {"start_mb": 17.109375, "peak_mb": 168.390625, "end_mb": 168.390625, "min_mb": 17.109375, "avg_mb": 104.48263888888889, "delta_mb": 151.28125, "peak_increase_mb": 151.28125}, "vms_memory": {"start_mb": 401280.328125, "peak_mb": 401427.984375, "end_mb": 401427.984375, "delta_mb": 147.65625}, "garbage_collection": {"start_count": 683, "end_count": 12, "total_collections": -671}, "memory_efficiency": {"peak_recovery_percentage": 0.0, "stable_memory": false, "no_memory_leaks": false}}, "status": "PASSED"}}, "summary": {"total_tests": 19, "passed_tests": 19, "failed_tests": 0, "success_rate": 100.0, "overall_status": "PASSED"}}