#!/usr/bin/env python3
"""
Comprehensive Test Suite for Memory Optimization Changes
Following Apache Airflow's Breeze Testing Guidelines

This script simulates the Breeze testing environment and runs comprehensive
validation of our memory optimization changes for Spark Kubernetes tests.
"""

import os
import sys
import subprocess
import time
import json
import psutil
import gc
from pathlib import Path
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BreezeCompatibleTestSuite:
    """
    Comprehensive test suite that follows Airflow's Breeze testing guidelines
    while working around ARM64 compatibility issues.
    """
    
    def __init__(self, airflow_root: str):
        self.airflow_root = Path(airflow_root)
        self.test_results = {}
        self.memory_metrics = {}
        self.start_time = time.time()
        
        # Test configuration following Breeze patterns
        self.test_config = {
            'unit_tests': {
                'spark_kubernetes': 'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py',
                'related_k8s': 'providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/',
            },
            'integration_tests': {
                'kubernetes_provider': 'providers/cncf/kubernetes/tests/',
            },
            'memory_monitoring': {
                'enabled': True,
                'gc_analysis': True,
                'leak_detection': True,
            }
        }
    
    def setup_environment(self) -> bool:
        """Setup testing environment following Breeze guidelines."""
        logger.info("🔧 Setting up Breeze-compatible testing environment...")

        try:
            # Change to Airflow root directory
            os.chdir(self.airflow_root)

            # Verify we're in the right directory
            if not (self.airflow_root / 'pyproject.toml').exists():
                logger.error("❌ Not in Airflow root directory")
                return False

            # Create absolute path for SQLite database
            db_path = self.airflow_root / 'test_airflow.db'

            # Set environment variables following Breeze patterns
            os.environ.update({
                'AIRFLOW_HOME': str(self.airflow_root),
                'PYTHONPATH': str(self.airflow_root),
                'AIRFLOW__CORE__UNIT_TEST_MODE': 'True',
                'AIRFLOW__CORE__LOAD_EXAMPLES': 'False',
                'AIRFLOW__CORE__LOAD_DEFAULT_CONNECTIONS': 'False',
                'AIRFLOW__DATABASE__SQL_ALCHEMY_CONN': f'sqlite:///{db_path}',
                'AIRFLOW__LOGGING__LOGGING_LEVEL': 'WARNING',
                'AIRFLOW__CORE__DAGS_FOLDER': str(self.airflow_root / 'dags'),
                'AIRFLOW__CORE__PLUGINS_FOLDER': str(self.airflow_root / 'plugins'),
            })

            logger.info("✅ Environment setup complete")
            return True

        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            return False
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests following Breeze testing patterns."""
        logger.info("🧪 Running Unit Tests (Breeze-style)...")
        
        results = {}
        
        # Test 1: Core Spark Kubernetes Tests
        logger.info("Running core Spark Kubernetes tests...")
        spark_test_file = self.test_config['unit_tests']['spark_kubernetes']
        
        cmd = [
            sys.executable, '-m', 'pytest',
            spark_test_file,
            '-v',
            '--tb=short',
            '--disable-warnings',
            '--junit-xml=files/test_result-spark-kubernetes.xml'
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root)
        duration = time.time() - start_time
        
        results['spark_kubernetes'] = {
            'returncode': result.returncode,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'passed': result.returncode == 0
        }
        
        # Test 2: Related Kubernetes Tests
        logger.info("Running related Kubernetes provider tests...")
        k8s_test_dir = self.test_config['unit_tests']['related_k8s']
        
        cmd = [
            sys.executable, '-m', 'pytest',
            k8s_test_dir,
            '-v',
            '--tb=short',
            '--disable-warnings',
            '--junit-xml=files/test_result-k8s-providers.xml',
            '--maxfail=5'  # Stop after 5 failures to save time
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.airflow_root)
        duration = time.time() - start_time
        
        results['kubernetes_providers'] = {
            'returncode': result.returncode,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'passed': result.returncode == 0
        }
        
        self.test_results['unit_tests'] = results
        return results
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests following Breeze patterns."""
        logger.info("🔗 Running Integration Tests (Breeze-style)...")
        
        results = {}
        
        # Integration test: Memory optimization functionality
        logger.info("Testing memory optimization integration...")
        
        try:
            # Import and test our factory functions
            sys.path.insert(0, str(self.airflow_root))
            
            from providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes import (
                _get_expected_k8s_dict,
                _get_expected_application_dict_with_labels
            )
            
            # Test factory function integration
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            # Create multiple objects to test memory behavior
            objects = []
            for i in range(100):
                k8s_dict = _get_expected_k8s_dict()
                app_dict = _get_expected_application_dict_with_labels(f'test_task_{i}')
                objects.append((k8s_dict, app_dict))
            
            mid_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            # Clear objects and force garbage collection
            objects.clear()
            gc.collect()
            
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            results['memory_optimization'] = {
                'start_memory_mb': start_memory,
                'mid_memory_mb': mid_memory,
                'end_memory_mb': end_memory,
                'memory_increase_mb': mid_memory - start_memory,
                'memory_recovered_mb': mid_memory - end_memory,
                'passed': True
            }
            
            logger.info(f"✅ Memory test: {start_memory:.1f}MB → {mid_memory:.1f}MB → {end_memory:.1f}MB")
            
        except Exception as e:
            logger.error(f"❌ Integration test failed: {e}")
            results['memory_optimization'] = {
                'error': str(e),
                'passed': False
            }
        
        self.test_results['integration_tests'] = results
        return results
    
    def run_system_tests(self) -> Dict[str, Any]:
        """Run system-level tests following Breeze patterns."""
        logger.info("🖥️  Running System Tests (Breeze-style)...")
        
        results = {}
        
        # System test: Import compatibility
        logger.info("Testing system-level import compatibility...")
        
        try:
            # Test that all imports work correctly
            import_tests = [
                'airflow.providers.cncf.kubernetes.operators.spark_kubernetes',
                'providers.cncf.kubernetes.tests.unit.cncf.kubernetes.operators.test_spark_kubernetes',
            ]
            
            for import_path in import_tests:
                try:
                    __import__(import_path)
                    logger.info(f"✅ Import successful: {import_path}")
                except ImportError as e:
                    logger.error(f"❌ Import failed: {import_path} - {e}")
                    raise
            
            results['import_compatibility'] = {
                'imports_tested': len(import_tests),
                'passed': True
            }
            
        except Exception as e:
            logger.error(f"❌ System test failed: {e}")
            results['import_compatibility'] = {
                'error': str(e),
                'passed': False
            }
        
        self.test_results['system_tests'] = results
        return results
    
    def monitor_memory_usage(self) -> Dict[str, Any]:
        """Monitor memory usage following Breeze monitoring patterns."""
        logger.info("📊 Monitoring Memory Usage (Breeze-style)...")
        
        process = psutil.Process()
        
        memory_info = {
            'rss_mb': process.memory_info().rss / 1024 / 1024,
            'vms_mb': process.memory_info().vms / 1024 / 1024,
            'percent': process.memory_percent(),
            'gc_stats': {
                'generation_0': gc.get_count()[0],
                'generation_1': gc.get_count()[1],
                'generation_2': gc.get_count()[2],
            }
        }
        
        self.memory_metrics = memory_info
        return memory_info
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report following Breeze reporting patterns."""
        logger.info("📋 Generating Comprehensive Test Report...")
        
        total_duration = time.time() - self.start_time
        
        report = {
            'test_suite': 'Breeze Compatible Memory Optimization Validation',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration_seconds': total_duration,
            'environment': {
                'python_version': sys.version,
                'airflow_root': str(self.airflow_root),
                'platform': sys.platform,
            },
            'test_results': self.test_results,
            'memory_metrics': self.memory_metrics,
            'summary': self._generate_summary()
        }
        
        return report
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate test summary."""
        total_tests = 0
        passed_tests = 0
        
        for test_category, tests in self.test_results.items():
            for test_name, result in tests.items():
                total_tests += 1
                if result.get('passed', False):
                    passed_tests += 1
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'overall_status': 'PASSED' if passed_tests == total_tests else 'FAILED'
        }

def main():
    """Main execution function."""
    airflow_root = '/Users/<USER>/oss/pr/airflow'
    
    logger.info("🚀 Starting Breeze-Compatible Test Suite...")
    
    test_suite = BreezeCompatibleTestSuite(airflow_root)
    
    # Setup environment
    if not test_suite.setup_environment():
        sys.exit(1)
    
    # Run all test phases
    test_suite.run_unit_tests()
    test_suite.run_integration_tests()
    test_suite.run_system_tests()
    test_suite.monitor_memory_usage()
    
    # Generate and save report
    report = test_suite.generate_report()
    
    # Save report to file
    report_file = Path(airflow_root) / 'breeze_compatibility_test_report.json'
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"📄 Report saved to: {report_file}")
    
    # Print summary
    summary = report['summary']
    logger.info(f"🎯 Test Summary: {summary['passed_tests']}/{summary['total_tests']} tests passed")
    logger.info(f"📈 Success Rate: {summary['success_rate']:.1f}%")
    logger.info(f"🏁 Overall Status: {summary['overall_status']}")
    
    return 0 if summary['overall_status'] == 'PASSED' else 1

if __name__ == '__main__':
    sys.exit(main())
