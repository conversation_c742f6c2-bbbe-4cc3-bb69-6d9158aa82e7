{"test_suite": "Focused Integration Test Suite", "timestamp": "2025-05-31 02:01:10", "total_duration_seconds": 31.136548280715942, "test_results": {"k8s_operators": {"pod_operator": {"returncode": 0, "duration_seconds": 26.064228057861328, "tests_passed": 168, "tests_failed": 0, "tests_error": 0, "total_tests": 168, "success_rate": 100.0, "status": "PASSED"}, "job_operator": {"returncode": 0, "duration_seconds": 4.025007963180542, "tests_passed": 56, "tests_failed": 0, "tests_error": 0, "total_tests": 56, "success_rate": 100.0, "status": "PASSED"}}, "memory_behavior": {"memory_isolation": {"start_memory_mb": 193.34375, "peak_memory_mb": 195.984375, "after_component_a_clear_mb": 195.984375, "end_memory_mb": 195.984375, "component_a_recovery_mb": 0.0, "component_b_recovery_mb": 0.0, "total_recovery_mb": 0.0, "objects_per_component": 400, "isolation_effective": false, "status": "PASSED"}, "factory_consistency": {"objects_created": 100, "unique_task_ids": 100, "all_task_ids_unique": true, "task_id_propagation_correct": true, "objects_independent": true, "status": "PASSED"}}, "compatibility": {"import_compatibility": {"functions_importable": true, "functions_callable": true, "return_types_correct": true, "structure_valid": true, "task_id_propagation": true, "backward_compatible": true, "status": "PASSED"}}}, "summary": {"total_tests": 5, "passed_tests": 5, "failed_tests": 0, "success_rate": 100.0, "overall_status": "PASSED"}}