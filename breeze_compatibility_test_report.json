{"timestamp": "2025-05-30T22:37:36.872804", "breeze_compatibility": {"breeze_available": false, "command_structure_ok": true, "environment_variables_ok": true, "overall_compatibility": false}, "test_results": [{"test_name": "Direct Test Equivalent", "breeze_available": false, "direct_test_success": true, "memory_usage_mb": 13.125, "duration_seconds": 7.75959587097168, "optimization_validated": false, "ci_ready": false}], "recommendations": ["Install Breeze for full CI environment testing", "Address compatibility issues before Breeze deployment"]}