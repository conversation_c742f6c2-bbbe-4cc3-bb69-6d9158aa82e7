{"test_suite": "Breeze Compatible Memory Optimization Validation", "timestamp": "2025-05-31 01:29:08", "duration_seconds": 6.180869817733765, "environment": {"python_version": "3.12.2 (v3.12.2:6abddd9f6a, Feb  6 2024, 17:02:06) [Clang 13.0.0 (clang-1300.0.29.30)]", "airflow_root": "/Users/<USER>/oss/pr/airflow", "platform": "darwin"}, "test_results": {"unit_tests": {"spark_kubernetes": {"returncode": 2, "duration": 3.5934910774230957, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.12.2, pytest-8.3.5, pluggy-1.6.0 -- /Users/<USER>/oss/pr/airflow/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/oss/pr/airflow\nconfigfile: pyproject.toml\nplugins: instafail-0.5.0, anyio-4.9.0, timeouts-1.2.1, time-machine-2.16.0, custom-exit-code-0.3.0, icdiff-0.9, rerunfailures-15.1, kgb-7.2, asyncio-0.25.0, mock-3.14.0, unordered-0.6.1, cov-6.1.1, xdist-3.6.1, requests-mock-1.12.1\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=function\nsetup timeout: 0.0s, execution timeout: 0.0s, teardown timeout: 0.0s\ncollecting ... collected 0 items / 1 error\n\n==================================== ERRORS ====================================\n_ ERROR collecting providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py _\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py:33: in <module>\n    from airflow import DAG\nairflow-core/src/airflow/__init__.py:79: in <module>\n    settings.initialize()\nairflow-core/src/airflow/settings.py:619: in initialize\n    configure_orm()\nairflow-core/src/airflow/settings.py:330: in configure_orm\n    raise AirflowConfigException(\nE   airflow.exceptions.AirflowConfigException: Cannot use relative path: `sqlite:///airflow.db` to connect to sqlite. Please use absolute path such as `sqlite:////tmp/airflow.db`.\n- generated xml file: /Users/<USER>/oss/pr/airflow/files/test_result-spark-kubernetes.xml -\n=========================== short test summary info ============================\nERROR providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_spark_kubernetes.py - airflow.exceptions.AirflowConfigException: Cannot use relative path: `sqlite:///airflow.db` to connect to sqlite. Please use absolute path such as `sqlite:////tmp/airflow.db`.\n!!!!!!!!!!!!!!!!!!!! Interrupted: 1 error during collection !!!!!!!!!!!!!!!!!!!!\n========================= 1 warning, 1 error in 0.53s ==========================\n", "stderr": "", "passed": false}, "kubernetes_providers": {"returncode": 1, "duration": 2.2555768489837646, "stdout": "============================= test session starts ==============================\nplatform darwin -- Python 3.12.2, pytest-8.3.5, pluggy-1.6.0 -- /Users/<USER>/oss/pr/airflow/.venv/bin/python\ncachedir: .pytest_cache\nrootdir: /Users/<USER>/oss/pr/airflow\nconfigfile: pyproject.toml\nplugins: instafail-0.5.0, anyio-4.9.0, timeouts-1.2.1, time-machine-2.16.0, custom-exit-code-0.3.0, icdiff-0.9, rerunfailures-15.1, kgb-7.2, asyncio-0.25.0, mock-3.14.0, unordered-0.6.1, cov-6.1.1, xdist-3.6.1, requests-mock-1.12.1\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=function\nsetup timeout: 0.0s, execution timeout: 0.0s, teardown timeout: 0.0s\ncollecting ... collected 16 items / 5 errors\n\n==================================== ERRORS ====================================\n_ ERROR collecting providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_custom_object_launcher.py _\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_custom_object_launcher.py:31: in <module>\n    from airflow.exceptions import AirflowException\nairflow-core/src/airflow/__init__.py:79: in <module>\n    settings.initialize()\nairflow-core/src/airflow/settings.py:619: in initialize\n    configure_orm()\nairflow-core/src/airflow/settings.py:330: in configure_orm\n    raise AirflowConfigException(\nE   airflow.exceptions.AirflowConfigException: Cannot use relative path: `sqlite:///airflow.db` to connect to sqlite. Please use absolute path such as `sqlite:////tmp/airflow.db`.\n_ ERROR collecting providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_job.py _\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:105: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nDuring handling of the above exception, another exception occurred:\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:560: in configure\n    formatters[name] = self.configure_formatter(\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:693: in configure_formatter\n    c = _resolve(cname)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:108: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nThe above exception was the direct cause of the following exception:\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_job.py:30: in <module>\n    from airflow.models import DAG, DagModel, DagRun, TaskInstance\nairflow-core/src/airflow/__init__.py:79: in <module>\n    settings.initialize()\nairflow-core/src/airflow/settings.py:613: in initialize\n    LOGGING_CLASS_PATH = configure_logging()\nairflow-core/src/airflow/logging_config.py:103: in configure_logging\n    raise e\nairflow-core/src/airflow/logging_config.py:98: in configure_logging\n    dictConfig(logging_config)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:914: in dictConfig\n    dictConfigClass(config).configure()\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:563: in configure\n    raise ValueError('Unable to configure '\nE   ValueError: Unable to configure formatter 'airflow'\n------------------------------- Captured stdout --------------------------------\n[2025-05-31T01:29:07.586+0530] {logging_config.py:100} ERROR - Unable to load the config, contains a configuration error.\n_ ERROR collecting providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_kueue.py _\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:105: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nDuring handling of the above exception, another exception occurred:\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:560: in configure\n    formatters[name] = self.configure_formatter(\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:693: in configure_formatter\n    c = _resolve(cname)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:108: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nThe above exception was the direct cause of the following exception:\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_kueue.py:26: in <module>\n    from airflow.providers.cncf.kubernetes.operators.job import KubernetesJobOperator\nairflow-core/src/airflow/__init__.py:79: in <module>\n    settings.initialize()\nairflow-core/src/airflow/settings.py:613: in initialize\n    LOGGING_CLASS_PATH = configure_logging()\nairflow-core/src/airflow/logging_config.py:103: in configure_logging\n    raise e\nairflow-core/src/airflow/logging_config.py:98: in configure_logging\n    dictConfig(logging_config)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:914: in dictConfig\n    dictConfigClass(config).configure()\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:563: in configure\n    raise ValueError('Unable to configure '\nE   ValueError: Unable to configure formatter 'airflow'\n------------------------------- Captured stdout --------------------------------\n[2025-05-31T01:29:07.622+0530] {logging_config.py:100} ERROR - Unable to load the config, contains a configuration error.\n_ ERROR collecting providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_pod.py _\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:105: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nDuring handling of the above exception, another exception occurred:\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:560: in configure\n    formatters[name] = self.configure_formatter(\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:693: in configure_formatter\n    c = _resolve(cname)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:108: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nThe above exception was the direct cause of the following exception:\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_pod.py:38: in <module>\n    from airflow.models import DAG, DagModel, DagRun, TaskInstance\nairflow-core/src/airflow/__init__.py:79: in <module>\n    settings.initialize()\nairflow-core/src/airflow/settings.py:613: in initialize\n    LOGGING_CLASS_PATH = configure_logging()\nairflow-core/src/airflow/logging_config.py:103: in configure_logging\n    raise e\nairflow-core/src/airflow/logging_config.py:98: in configure_logging\n    dictConfig(logging_config)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:914: in dictConfig\n    dictConfigClass(config).configure()\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:563: in configure\n    raise ValueError('Unable to configure '\nE   ValueError: Unable to configure formatter 'airflow'\n------------------------------- Captured stdout --------------------------------\n[2025-05-31T01:29:07.657+0530] {logging_config.py:100} ERROR - Unable to load the config, contains a configuration error.\n_ ERROR collecting providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_resource.py _\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:105: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nDuring handling of the above exception, another exception occurred:\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:560: in configure\n    formatters[name] = self.configure_formatter(\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:693: in configure_formatter\n    c = _resolve(cname)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:108: in _resolve\n    found = getattr(found, n)\nE   AttributeError: partially initialized module 'airflow' has no attribute 'utils' (most likely due to a circular import)\n\nThe above exception was the direct cause of the following exception:\nproviders/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_resource.py:25: in <module>\n    from airflow import DAG\nairflow-core/src/airflow/__init__.py:79: in <module>\n    settings.initialize()\nairflow-core/src/airflow/settings.py:613: in initialize\n    LOGGING_CLASS_PATH = configure_logging()\nairflow-core/src/airflow/logging_config.py:103: in configure_logging\n    raise e\nairflow-core/src/airflow/logging_config.py:98: in configure_logging\n    dictConfig(logging_config)\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:914: in dictConfig\n    dictConfigClass(config).configure()\n/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/logging/config.py:563: in configure\n    raise ValueError('Unable to configure '\nE   ValueError: Unable to configure formatter 'airflow'\n------------------------------- Captured stdout --------------------------------\n[2025-05-31T01:29:07.702+0530] {logging_config.py:100} ERROR - Unable to load the config, contains a configuration error.\n- generated xml file: /Users/<USER>/oss/pr/airflow/files/test_result-k8s-providers.xml -\n=========================== short test summary info ============================\nERROR providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_custom_object_launcher.py - airflow.exceptions.AirflowConfigException: Cannot use relative path: `sqlite:///airflow.db` to connect to sqlite. Please use absolute path such as `sqlite:////tmp/airflow.db`.\nERROR providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_job.py - ValueError: Unable to configure formatter 'airflow'\nERROR providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_kueue.py - ValueError: Unable to configure formatter 'airflow'\nERROR providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_pod.py - ValueError: Unable to configure formatter 'airflow'\nERROR providers/cncf/kubernetes/tests/unit/cncf/kubernetes/operators/test_resource.py - ValueError: Unable to configure formatter 'airflow'\n!!!!!!!!!!!!!!!!!!!!!!!!!! stopping after 5 failures !!!!!!!!!!!!!!!!!!!!!!!!!!!\n========================= 1 warning, 5 errors in 0.45s =========================\n", "stderr": "", "passed": false}}, "integration_tests": {"memory_optimization": {"error": "Cannot use relative path: `sqlite:///airflow.db` to connect to sqlite. Please use absolute path such as `sqlite:////tmp/airflow.db`.", "passed": false}}, "system_tests": {"import_compatibility": {"error": "Unable to configure formatter 'airflow'", "passed": false}}}, "memory_metrics": {"rss_mb": 103.71875, "vms_mb": 401495.203125, "percent": 0.562710232204861, "gc_stats": {"generation_0": 204, "generation_1": 4, "generation_2": 2}}, "summary": {"total_tests": 4, "passed_tests": 0, "failed_tests": 4, "success_rate": 0.0, "overall_status": "FAILED"}}