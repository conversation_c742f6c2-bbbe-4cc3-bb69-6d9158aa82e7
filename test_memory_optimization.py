#!/usr/bin/env python3

import sys
import os
import logging
import tracemalloc
import gc
from unittest import mock

# Add necessary paths
sys.path.insert(0, '/Users/<USER>/oss/pr/airflow/providers/cncf/kubernetes/src')
sys.path.insert(0, '/Users/<USER>/oss/pr/airflow/airflow-core/src')

from airflow.providers.cncf.kubernetes.operators.spark_kubernetes import SparkKubernetesOperator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("memory_test")

def run_label_test(large_spec=False):
    """Test the label application with memory tracking"""
    # Start memory tracking
    tracemalloc.start()
    
    # Create a test job spec
    job_spec = {
        "apiVersion": "sparkoperator.k8s.io/v1beta2",
        "kind": "SparkApplication",
        "metadata": {"name": "test-app"},
        "spec": {
            "driver": {"labels": {}},
            "executor": {"labels": {}}
        }
    }
    
    # Make the spec larger to test memory usage if requested
    if large_spec:
        logger.info("Creating large spec for memory testing...")
        # Add lots of dummy data to increase size
        for i in range(1000):
            job_spec[f"config_{i}"] = {f"param_{j}": f"value_{j}" for j in range(100)}
    
    # Create operator with reattach_on_restart=True
    op = SparkKubernetesOperator(
        template_spec=job_spec,
        kubernetes_conn_id='kubernetes_default',
        task_id='test_task',
        reattach_on_restart=True
    )
    
    # Create a mock context
    mock_context = {
        'dag': mock.MagicMock(),
        'run_id': 'manual__2023-01-01T00:00:00+00:00',
        'ti': mock.MagicMock(),
        'task_instance': mock.MagicMock()
    }
    mock_context['dag'].dag_id = 'test_dag'
    mock_context['ti'].dag_id = 'test_dag'
    mock_context['ti'].task_id = 'test_task'
    mock_context['ti'].try_number = 1
    
    # Create a mock hook and client to avoid actual Kubernetes calls
    op.hook = mock.MagicMock()
    op.client = mock.MagicMock()
    op.custom_obj_api = mock.MagicMock()
    op.launcher = mock.MagicMock()
    op.pod = mock.MagicMock()
    
    # Force garbage collection before running
    gc.collect()
    
    # Capture memory snapshot before label processing
    snapshot1 = tracemalloc.take_snapshot()
    
    try:
        # Execute the method that applies the labels
        op.execute(mock_context)
        
        # Capture memory after label processing
        snapshot2 = tracemalloc.take_snapshot()
        
        # Get memory statistics
        current, peak = tracemalloc.get_traced_memory()
        logger.info(f"Current memory usage: {current / 1024:.2f} KB")
        logger.info(f"Peak memory usage: {peak / 1024:.2f} KB")
        
        # Find biggest memory users
        top_stats = snapshot2.compare_to(snapshot1, 'lineno')
        logger.info("Top 5 memory changes:")
        for stat in top_stats[:5]:
            logger.info(f"{stat}")
            
        logger.info("✅ Test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        tracemalloc.stop()

def test_find_spark_job():
    """Test the find_spark_job method"""
    logger.info("Testing find_spark_job method...")
    
    # Create operator
    op = SparkKubernetesOperator(
        template_spec={},
        kubernetes_conn_id='kubernetes_default',
        task_id='test_task',
        reattach_on_restart=True
    )
    
    # Create a mock context
    mock_context = {
        'dag': mock.MagicMock(),
        'run_id': 'manual__2023-01-01T00:00:00+00:00',
        'ti': mock.MagicMock(),
        'task_instance': mock.MagicMock()
    }
    mock_context['dag'].dag_id = 'test_dag'
    mock_context['ti'].dag_id = 'test_dag'
    mock_context['ti'].task_id = 'test_task'
    mock_context['ti'].try_number = 1
    
    # Mock Kubernetes client response
    mock_pod = mock.MagicMock()
    mock_pod.metadata.name = "test-pod"
    mock_pod.metadata.labels = {
        "dag_id": "test_dag",
        "task_id": "test_task",
        "run_id": "manual__2023-01-01T00:00:00+00:00",
        "try_number": "1",
        "spark_kubernetes_operator": "True"
    }
    
    mock_pod_list = mock.MagicMock()
    mock_pod_list.items = [mock_pod]
    
    # Set up client mock
    op.client = mock.MagicMock()
    op.client.list_namespaced_pod.return_value = mock_pod_list
    
    # Call the method
    result = op.find_spark_job(mock_context)
    
    # Verify results
    assert result is not None, "Should have found a pod"
    assert result == mock_pod, "Wrong pod returned"
    
    # Check that the client was called with the right parameters
    op.client.list_namespaced_pod.assert_called_once()
    
    logger.info("✅ find_spark_job test passed!")
    return True

def test_get_ti_pod_labels():
    """Test the _get_ti_pod_labels method"""
    logger.info("Testing _get_ti_pod_labels method...")
    
    # Create a mock context
    mock_context = {
        'dag': mock.MagicMock(),
        'run_id': 'manual__2023-01-01T00:00:00+00:00',
        'ti': mock.MagicMock(),
        'task_instance': mock.MagicMock()
    }
    mock_context['dag'].dag_id = 'test_dag'
    mock_context['dag'].is_subdag = False
    mock_context['ti'].dag_id = 'test_dag'
    mock_context['ti'].task_id = 'test_task'
    mock_context['ti'].try_number = 1
    mock_context['ti'].map_index = -1
    
    # Call the method
    labels = SparkKubernetesOperator._get_ti_pod_labels(mock_context)
    
    # Check results
    assert "dag_id" in labels, "Missing dag_id label"
    assert "task_id" in labels, "Missing task_id label"
    assert "run_id" in labels, "Missing run_id label"
    assert "try_number" in labels, "Missing try_number label"
    assert "spark_kubernetes_operator" in labels, "Missing spark_kubernetes_operator label"
    
    # Test with map_index
    mock_context['ti'].map_index = 5
    labels = SparkKubernetesOperator._get_ti_pod_labels(mock_context)
    assert "map_index" in labels, "Missing map_index label"
    
    # Test with subdag
    mock_context['dag'].is_subdag = True
    mock_context['dag'].parent_dag = mock.MagicMock()
    mock_context['dag'].parent_dag.dag_id = "parent_dag"
    labels = SparkKubernetesOperator._get_ti_pod_labels(mock_context)
    assert "parent_dag_id" in labels, "Missing parent_dag_id label"
    
    logger.info("✅ _get_ti_pod_labels test passed!")
    return True

if __name__ == '__main__':
    print("Running memory optimization tests for SparkKubernetesOperator")
    print("==========================================================\n")
    
    all_passed = True
    
    print("\n1. Testing _get_ti_pod_labels method...")
    try:
        result = test_get_ti_pod_labels()
        all_passed = all_passed and result
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        all_passed = False
    
    print("\n2. Testing find_spark_job method...")
    try:
        result = test_find_spark_job()
        all_passed = all_passed and result
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        all_passed = False
    
    print("\n3. Testing standard job memory usage...")
    try:
        result = run_label_test(large_spec=False)
        all_passed = all_passed and result
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        all_passed = False
    
    print("\n4. Testing large job memory usage...")
    try:
        result = run_label_test(large_spec=True)
        all_passed = all_passed and result
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        traceback.print_exc()
        all_passed = False
    
    print("\n==========================================================\n")
    if all_passed:
        print("✅ All tests passed! Memory optimizations are working properly.")
        print("   Your changes should fix the memory issues in GitHub Actions.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
